import { WEBHOST, WEBNAME } from "@/lib/constants";
import { createOpenAI } from "@ai-sdk/openai";

// Initialize OpenAI provider with API key from environment variables
export const openaiProvider = createOpenAI({
	baseURL: "https://openrouter.ai/api/v1",
	apiKey: process.env.OPENROUTER_KEY,
	headers: {
		"HTTP-Referer": WEBHOST,
		"X-Title": WEBNAME,
		"Content-Type": "application/json",
	},
});

export const openaiModel = openaiProvider("openai/gpt-4.1-nano");
