import { getKVKeyBlog, getKVKeyBlogHeads, KV_KEY_BLOG_CATEGORIES } from "@/lib/utils";
import { DURATION_1_MONTH, DURATION_1_WEEK } from "@/lib/constants";
import { getDB } from "./db/db-client.server";
import { blogCategorySchema, blogPostSchema, blogPostTranslationSchema } from "./db/schema.server";
import { and, eq, desc } from "drizzle-orm";
import { getValue, setValue } from "./kv/redis-upstash.server";
import superjson from "superjson";
import { blogDetailSchema, BlogDetailType, BlogPublicHead, blogPublicHeadSchema, BlogStatus } from "@/@types/admin/blog/blog";

const getBlogCategories = async () => {
	let blogCategories: any[] = [];
	const cacheKeyBlogCategories = KV_KEY_BLOG_CATEGORIES;
	const kvDataCategories = (await getValue(cacheKeyBlogCategories)) as any;
	if (kvDataCategories) {
		try {
			blogCategories = kvDataCategories;
		} catch (error) {
			console.log("[getBlogHeadsWithCategory] parse blog categories data from kv error:", error);
		}
	}
	if (blogCategories.length === 0) {
		const db = getDB();
		blogCategories = await db
			.select({
				id: blogCategorySchema.id,
				slug: blogCategorySchema.slug,
				name: blogCategorySchema.name,
			})
			.from(blogCategorySchema)
			.orderBy(desc(blogCategorySchema.id));
		// console.log(categories);
		if (blogCategories.length > 0) {
			await setValue(cacheKeyBlogCategories, JSON.stringify(blogCategories), DURATION_1_MONTH);
		}
	}
	return blogCategories;
};

// blog 列表页面获取某个语言的所有blogs
export async function getBlogHeadsWithCategory(lang: string = "en", page: number = 1): Promise<{ blogHeads: BlogPublicHead[]; blogCategories: any[] }> {
	const pageSize = 20;
	const offset = (page - 1) * pageSize;

	// 1. Get blogs
	let blogHeads: BlogPublicHead[] = [];
	//先从kv中获取
	const cacheKeyBlogHead = getKVKeyBlogHeads(lang, page);
	const kvDataBlogHead = (await getValue(cacheKeyBlogHead)) as any;
	if (kvDataBlogHead) {
		try {
			const blogHeadsParse = superjson.deserialize(kvDataBlogHead) as BlogPublicHead[];
			// console.log("blogHeadsParse:", blogHeadsParse);
			blogPublicHeadSchema.array().parse(blogHeadsParse);
			blogHeads = blogHeadsParse;
		} catch (error) {
			console.log("[getBlogHeadsWithCategory] parse blog head data from kv error:", error);
		}
	}

	//再从db中获取

	if (blogHeads.length === 0) {
		const db = getDB();
		const postsWithTranslations = await db.query.blogPostTranslationSchema.findMany({
			columns: {
				id: true,
				postId: true,
				lang: true,
				status: true,
				image: true,
				title: true,
				intro: true,
				publishedAt: true,
			},
			where: and(eq(blogPostTranslationSchema.lang, lang), eq(blogPostTranslationSchema.status, BlogStatus.Published)),
			with: {
				post: {
					columns: {
						slug: true,
						image: true,
						categoryId: true,
					},
				},
			},
			orderBy: [desc(blogPostTranslationSchema.publishedAt)],
			limit: pageSize,
			offset: offset,
		});
		if (postsWithTranslations.length > 0) {
			blogHeads = postsWithTranslations.map((postWithTranslation) => ({
				categoryId: postWithTranslation.post.categoryId,
				slug: postWithTranslation.post.slug,
				lang: postWithTranslation.lang,
				title: postWithTranslation.title,
				image: postWithTranslation.image ?? postWithTranslation.post.image,
				intro: postWithTranslation.intro,
				publishedAt: postWithTranslation.publishedAt,
			}));
			await setValue(cacheKeyBlogHead, superjson.stringify(blogHeads), DURATION_1_WEEK);
		}
	}

	// 2. Get categories
	const blogCategories: any[] = await getBlogCategories();

	return { blogHeads, blogCategories };
}

// 获取单篇blog
export async function getBlogWithCategory(lang: string, slug: string): Promise<{ blog: BlogDetailType | null; blogCategories: any[] }> {
	// 1. Get blog
	let blogDetail: BlogDetailType | null = null;
	//先从kv中获取
	let cacheKeyBlog = getKVKeyBlog(lang, slug);
	const kvData = (await getValue(cacheKeyBlog)) as any;
	if (kvData) {
		try {
			const blogDetailParse = superjson.deserialize(kvData) as BlogDetailType;
			// console.log("blogHeadsParse:", blogHeadsParse);
			blogDetailSchema.parse(blogDetailParse);
			blogDetail = blogDetailParse;
		} catch (error) {
			console.log("[getBlogWithCategory] parse blog detail data from kv error:", error);
		}
	}
	//再从db中获取
	if (!blogDetail) {
		const db = getDB();
		const postWithSpecificTranslation = await db.query.blogPostSchema.findFirst({
			where: eq(blogPostSchema.slug, slug),
			with: {
				translations: {
					where: (translation, { eq }) => eq(translation.lang, lang),
					limit: 1,
				},
			},
		});
		if (postWithSpecificTranslation && postWithSpecificTranslation.translations[0].status === BlogStatus.Published) {
			blogDetail = {
				categoryId: postWithSpecificTranslation.categoryId,
				slug: postWithSpecificTranslation.slug,
				lang: postWithSpecificTranslation.translations[0].lang,
				metaTitle: postWithSpecificTranslation.translations[0].metaTitle,
				metaDescription: postWithSpecificTranslation.translations[0].metaDescription,
				title: postWithSpecificTranslation.translations[0].title,
				image: postWithSpecificTranslation.translations[0].image ?? postWithSpecificTranslation.image,
				intro: postWithSpecificTranslation.translations[0].intro,
				html: postWithSpecificTranslation.translations[0].html,
				publishedAt: postWithSpecificTranslation.translations[0].publishedAt,
				status: postWithSpecificTranslation.translations[0].status,
			};
			await setValue(cacheKeyBlog, superjson.stringify(blogDetail), DURATION_1_MONTH);
		}
	}
	if (!blogDetail) {
		return { blog: null, blogCategories: [] };
	}

	// 2. Get categories
	const blogCategories: any[] = await getBlogCategories();

	return { blog: blogDetail, blogCategories };
}
