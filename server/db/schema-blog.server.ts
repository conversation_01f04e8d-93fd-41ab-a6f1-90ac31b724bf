import { sqliteTable, integer, text, index, uniqueIndex } from "drizzle-orm/sqlite-core";
import { relations } from "drizzle-orm";

// ==============blog_category====================
export const blogCategorySchema = sqliteTable("blog_category", {
	id: integer("id", { mode: "number" }).primaryKey({ autoIncrement: true }),
	slug: text("slug").unique().notNull(),
	name: text("name"),
	description: text("description"),

	createdAt: integer("created_at", { mode: "timestamp_ms" })
		.notNull()
		.$defaultFn(() => new Date()),
	updatedAt: integer("updated_at", { mode: "timestamp_ms" })
		.notNull()
		.$defaultFn(() => new Date())
		.$onUpdateFn(() => new Date()),
});
export type BlogCategory = typeof blogCategorySchema.$inferSelect;
export type NewBlogCategory = typeof blogCategorySchema.$inferInsert;

// 1. 核心文章表 (Core Post Table)
// 存储不依赖于语言的共享数据
export const blogPostSchema = sqliteTable(
	"blog_post",
	{
		id: integer("id", { mode: "number" }).primaryKey({ autoIncrement: true }),
		slug: text("slug").unique().notNull(),
		categoryId: integer("category_id", { mode: "number" }),

		title: text("title"),
		image: text("image"),

		createdAt: integer("created_at", { mode: "timestamp_ms" })
			.notNull()
			.$defaultFn(() => new Date()),
		updatedAt: integer("updated_at", { mode: "timestamp_ms" })
			.notNull()
			.$defaultFn(() => new Date())
			.$onUpdateFn(() => new Date()),
	},
	(table) => [index("idx_blog_post_category").on(table.categoryId), index("idx_blog_post_slug").on(table.slug)],
);

// 2. 翻译内容表 (Translation Table)
// 存储特定语言的内容
export const blogPostTranslationSchema = sqliteTable(
	"blog_post_translation",
	{
		id: integer("id", { mode: "number" }).primaryKey({ autoIncrement: true }),
		postId: integer("post_id", { mode: "number" })
			.notNull()
			.references(() => blogPostSchema.id, { onDelete: "cascade" }), // 设置外键和级联删除

		lang: text("lang").default("en").notNull(),
		status: integer("status", { mode: "number" }).default(0).notNull(), // 0: draft, 1: published

		image: text("image"),

		title: text("title").notNull(),
		metaTitle: text("meta_title"),
		metaDescription: text("meta_description"),
		intro: text("intro"),
		html: text("html"),

		publishedAt: integer("publish_date", { mode: "timestamp_ms" })
			.notNull()
			.$defaultFn(() => new Date()),
		createdAt: integer("created_at", { mode: "timestamp_ms" })
			.notNull()
			.$defaultFn(() => new Date()),
		updatedAt: integer("updated_at", { mode: "timestamp_ms" })
			.notNull()
			.$defaultFn(() => new Date())
			.$onUpdateFn(() => new Date()),
	},
	(table) => [
		index("idx_blog_post_translation_post_id").on(table.postId),
		index("idx_blog_post_translation_lang").on(table.lang),
		uniqueIndex("uidx_blog_post_translation_post_id_lang").on(table.postId, table.lang),
		index("idx_blog_post_translation_published_at").on(table.publishedAt),
	],
);

// 3. 定义关系 (Defining Relations)
// 这对于使用Drizzle进行JOIN查询至关重要
export const blogPostRelations = relations(blogPostSchema, ({ many }) => ({
	translations: many(blogPostTranslationSchema),
}));

export const blogPostTranslationRelations = relations(blogPostTranslationSchema, ({ one }) => ({
	post: one(blogPostSchema, {
		fields: [blogPostTranslationSchema.postId],
		references: [blogPostSchema.id],
	}),
}));

// 导出类型
export type BlogPost = typeof blogPostSchema.$inferSelect;
export type NewBlogPost = typeof blogPostSchema.$inferInsert;

export type BlogPostTranslation = typeof blogPostTranslationSchema.$inferSelect;
export type NewBlogPostTranslation = typeof blogPostTranslationSchema.$inferInsert;
