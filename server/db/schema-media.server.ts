import { sqliteTable, integer, text, index } from "drizzle-orm/sqlite-core";

// ==============media generate task ===============
export const mediaTaskSchema = sqliteTable(
	"media_task",
	{
		id: integer("id", { mode: "number" }).primaryKey({ autoIncrement: true }),
		userId: text("user_uid").notNull(),
		status: integer("status", { mode: "number" }).default(0).notNull(),

		type: text("type").default("image").notNull(), // image, video
		tool: text("tool"),
		mediaHeadUid: text("media_head_uid"),
		thirdRequestId: text("third_request_id"), // API  request id

		visibility: integer("visibility", { mode: "boolean" }).default(true).notNull(),
		prompt: text("prompt"),
		videoDuration: integer("video_duration", { mode: "number" }),
		aspectRatio: text("aspect_ratio"),
		seed: text("seed"),
		requestBody: text("request_body"),

		creditsSources: text("credits_source"),
		ip: text("ip"),
		remark: text("remark"),
		error: text("error"),
		createdAt: integer("created_at", { mode: "timestamp_ms" })
			.$defaultFn(() => new Date())
			.notNull(),
		updatedAt: integer("updated_at", { mode: "timestamp_ms" })
			.$defaultFn(() => new Date())
			.$onUpdateFn(() => new Date())
			.notNull(),
	},
	(table) => [index("idx_media_task_user_uid").on(table.userId), index("idx_media_task_third_request_id").on(table.thirdRequestId)],
);
export type MediaTask = typeof mediaTaskSchema.$inferSelect;
export type NewMediaTask = typeof mediaTaskSchema.$inferInsert;
// ==============media result====================
export const mediaHeadSchema = sqliteTable(
	"media_head",
	{
		id: integer("id", { mode: "number" }).primaryKey({ autoIncrement: true }),
		uid: text("uid").unique().notNull(),
		userId: text("user_uid").notNull(),
		status: text("status"), // like: generating, failed, success

		type: text("type").default("image").notNull(), // image, video
		tool: text("tool"),
		taskId: integer("task_id"),

		visibility: integer("visibility", { mode: "boolean" }).default(true).notNull(),
		prompt: text("prompt"),
		videoDuration: integer("video_duration", { mode: "number" }),
		aspectRatio: text("aspect_ratio"),
		seed: text("seed"),

		creditsSources: text("credits_source"),
		remark: text("remark"),
		errorReason: text("error_reason"),
		createdAt: integer("created_at", { mode: "timestamp_ms" })
			.notNull()
			.$defaultFn(() => new Date()),
		updatedAt: integer("updated_at", { mode: "timestamp_ms" })
			.notNull()
			.$defaultFn(() => new Date())
			.$onUpdateFn(() => new Date()),
	},
	(table) => [
		index("idx_media_head_user_uid").on(table.userId),
		index("idx_media_head_status").on(table.status),
		index("idx_media_head_type").on(table.type),
	],
);
export type MediaHead = typeof mediaHeadSchema.$inferSelect;
export type NewMediaHead = typeof mediaHeadSchema.$inferInsert;

export const mediaItemSchema = sqliteTable(
	"media_item",
	{
		id: integer("id", { mode: "number" }).primaryKey({ autoIncrement: true }),
		uid: text("uid").unique().notNull(),
		userId: text("user_uid").notNull(),

		mediaHeadUid: text("media_head_uid").notNull(),
		type: text("type").default("image").notNull(),

		visibility: integer("visibility", { mode: "boolean" }).default(true).notNull(), // false: private, true: public
		mediaPath: text("media_path"),

		createdAt: integer("created_at", { mode: "timestamp_ms" })
			.notNull()
			.$defaultFn(() => new Date()),
		updatedAt: integer("updated_at", { mode: "timestamp_ms" })
			.notNull()
			.$defaultFn(() => new Date())
			.$onUpdateFn(() => new Date()),
	},
	(table) => [
		index("idx_media_item_user_uid").on(table.userId),
		index("idx_media_item_media_head_uid").on(table.mediaHeadUid),
		index("idx_media_item_visibility").on(table.visibility),
		index("idx_media_item_type").on(table.type),
		index("idx_media_item_created_at").on(table.createdAt),
	],
);
export type MediaItem = typeof mediaItemSchema.$inferSelect;
export type NewMediaItem = typeof mediaItemSchema.$inferInsert;
