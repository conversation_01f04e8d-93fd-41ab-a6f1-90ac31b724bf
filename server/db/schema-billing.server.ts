import { sqliteTable, integer, text, index } from "drizzle-orm/sqlite-core";

// ============== order ====================
export const orderSchema = sqliteTable(
	"order",
	{
		id: integer("id", { mode: "number" }).primaryKey({ autoIncrement: true }),
		userId: text("user_uid").notNull(),

		orderId: text("order_id").unique().notNull(),
		source: text("source").notNull(), // polar, lmsqueezy, cream, etc.
		status: text("status").notNull(),
		billingReason: text("billing_reason"), //在付费成功的order.created中赋值。 type: purchase, subscription_create, subscription_cycle, subscription_update

		subscriptionId: text("subscription_id"),
		checkoutId: text("checkout_id"),
		productId: text("product_id"),

		currency: text("currency"), // usd, etc.
		subtotalAmount: integer("subtotal_amount", { mode: "number" }), // Amount in cents, before discounts and taxes.
		discountAmount: integer("discount_amount", { mode: "number" }), // Discount amount in cents.
		taxAmount: integer("tax_amount", { mode: "number" }), // Sales tax amount in cents.
		netAmount: integer("net_amount", { mode: "number" }), // Amount in cents, after discounts but before taxes.
		totalAmount: integer("total_amount", { mode: "number" }), // Amount in cents, after discounts and taxes.
		refundedAmount: integer("refunded_amount", { mode: "number" }), // Amount refunded in cents.
		refundedTaxAmount: integer("refunded_tax_amount", { mode: "number" }), // Sales tax refunded in cents.

		createdAt: integer("created_at", { mode: "timestamp_ms" })
			.notNull()
			.$defaultFn(() => new Date()),
		updatedAt: integer("updated_at", { mode: "timestamp_ms" })
			.notNull()
			.$defaultFn(() => new Date())
			.$onUpdateFn(() => new Date()),
	},
	(table) => {
		return [index("idx_order_user_uid").on(table.userId)];
	},
);
export type Order = typeof orderSchema.$inferSelect;
export type NewOrder = typeof orderSchema.$inferInsert;

// ==============subscription====================
export const subscriptionSchema = sqliteTable(
	"subscription",
	{
		id: integer("id", { mode: "number" }).primaryKey({ autoIncrement: true }),
		userId: text("user_uid").notNull(),

		subscriptionId: text("subscription_id").unique().notNull(),
		source: text("source").notNull(), // polar, lmsqueezy, cream, etc.
		status: text("status").notNull(),
		recurringInterval: text("recurring_interval").notNull(), // month, year

		productId: text("product_id").notNull(),
		checkoutId: text("checkout_id"),

		currentPeriodStartAt: integer("current_period_start_at", { mode: "timestamp_ms" }).notNull(), // The start timestamp of the current billing period.
		currentPeriodEndAt: integer("current_period_end_at", { mode: "timestamp_ms" }), // The end timestamp of the current billing period.
		cancelAtPeriodEnd: integer("cancel_at_period_end", { mode: "boolean" }).default(false).notNull(), // Whether the subscription will be canceled at the end of the current period.
		canceledAt: integer("canceled_at", { mode: "timestamp_ms" }),
		startAt: integer("start_at", { mode: "timestamp_ms" }), // The timestamp when the subscription started.
		endAt: integer("end_at", { mode: "timestamp_ms" }), // The timestamp when the subscription will end.
		endedAt: integer("ended_at", { mode: "timestamp_ms" }), // The timestamp when the subscription ended.

		customerCancellationReason: text("customer_cancellation_reason"), // The reason for the customer's cancellation.

		createdAt: integer("created_at", { mode: "timestamp_ms" })
			.notNull()
			.$defaultFn(() => new Date()),
		updatedAt: integer("updated_at", { mode: "timestamp_ms" })
			.notNull()
			.$defaultFn(() => new Date())
			.$onUpdateFn(() => new Date()),
	},
	(table) => [index("idx_subscription_user_uid").on(table.userId)],
);
export type Subscription = typeof subscriptionSchema.$inferSelect; // return type when queried
export type NewSubscription = typeof subscriptionSchema.$inferInsert; // insert type
