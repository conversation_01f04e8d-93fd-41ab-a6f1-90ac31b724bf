import { drizzle } from "drizzle-orm/libsql";
import * as schema from "./schema.server";

export const getDB = () => {
	return drizzle({
		connection: {
			url: process.env.DATABASE_URL!,
			authToken: process.env.DATABASE_AUTH_TOKEN,
		},
		logger: process.env.NODE_ENV === "development",
		schema,
	});
};

// export const db = drizzle({
// 	connection: {
// 		url: process.env.DATABASE_URL!,
// 		authToken: process.env.DATABASE_AUTH_TOKEN!,
// 	},
// 	logger: process.env.NODE_ENV === "development",
// });
