import { getDB } from "./db/db-client.server";
import { eq, desc } from "drizzle-orm";
import superjson from "superjson";
import { MediaTask, mediaTaskSchema, MediaHead, mediaHeadSchema, MediaItem, mediaItemSchema } from "./db/schema.server";
import { getKVKeyMediaTask } from "@/lib/utils";
import { getValue, setValue } from "./kv/redis-upstash.server";
import { DURATION_2_HOUR } from "@/lib/constants";

export async function getVoiceModelTask(requestId: string, userId: string | null): Promise<MediaTask | null> {
	let mediaTask: MediaTask | null = null;

	//先从kv中获取
	const cacheKey = getKVKeyMediaTask(requestId);
	const kvDataMediaTask = (await getValue(cacheKey)) as any;
	if (kvDataMediaTask) {
		try {
			mediaTask = superjson.deserialize(kvDataMediaTask) as MediaTask;
		} catch (error) {
			console.error("[getMediaTask] parse media task data from redis error:", error);
		}
	}

	//再从db中获取
	if (!mediaTask) {
		const db = getDB();
		const [newMediaTask]: MediaTask[] = await db.select().from(mediaTaskSchema).where(eq(mediaTaskSchema.thirdRequestId, requestId));
		if (newMediaTask) {
			await setValue(cacheKey, superjson.stringify(newMediaTask), DURATION_2_HOUR);
			mediaTask = newMediaTask;
		}
	}

	if (!mediaTask) {
		return null;
	}

	if (userId && mediaTask.userId !== userId) {
		return null;
	}

	return mediaTask;
}

export async function getMediaHeadRealtime(mediaHeadUid: string, userId: string | null): Promise<MediaHead | null> {
	const db = getDB();
	const [mediaHead]: MediaHead[] = await db.select().from(mediaHeadSchema).where(eq(mediaHeadSchema.uid, mediaHeadUid));
	if (!mediaHead) {
		return null;
	}

	if (userId && mediaHead.userId !== userId) {
		return null;
	}

	return mediaHead;
}
export async function getMediaItemsRealtime(mediaHeadUid: string, userId: string | null): Promise<MediaItem[] | null> {
	const db = getDB();
	const mediaItems: MediaItem[] = await db.select().from(mediaItemSchema).where(eq(mediaItemSchema.mediaHeadUid, mediaHeadUid));
	if (mediaItems.length === 0) {
		return null;
	}

	if (userId && mediaItems[0].userId !== userId) {
		return null;
	}

	return mediaItems;
}
