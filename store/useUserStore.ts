import { create } from "zustand";
import { UserInfoDB } from "@/@types/user";
import { ofetch } from "ofetch";
import { handleError } from "@/@types/error";
import { userHasPaid } from "@/lib/utils-user";
import { MembershipID } from "@/@types/membership-type";

type UserStore = {
	user: UserInfoDB | null;
	isLoaded: boolean;
	hasPaid: boolean;
	creditsAll: number;
	membershipLevel: string;
	setUser: (user: UserInfoDB | null) => void;
	refreshUser: () => Promise<void>;
};
export const useUserStore = create<UserStore>((set) => {
	return {
		user: null,
		isLoaded: false,
		hasPaid: false,
		creditsAll: 0,
		membershipLevel: "Free",
		setUser: (user: UserInfoDB | null) => {
			let hasPaid = false;
			let membershipLevel = "Free";
			if (user) {
				hasPaid = userHasPaid(user.membershipId, user.creditOneTimeEndsAt);
				membershipLevel = hasPaid && user.membershipId === MembershipID.Free ? "Onetime" : user.membershipFormatted;
			}
			set({
				user: user,
				isLoaded: true,
				hasPaid: hasPaid,
				creditsAll: user ? user.creditFree + user.creditOneTime + user.creditSubscription : 0,
				membershipLevel: membershipLevel,
			});
		},
		refreshUser: async () => {
			const { status, message, userInfo } = await ofetch("/api/v1/user", {
				method: "POST",
				body: {
					type: "credits",
				},
			});
			handleError(status, message);
			let hasPaid = false;
			let membershipLevel = "Free";
			if (userInfo) {
				hasPaid = userHasPaid(userInfo.membershipId, userInfo.creditOneTimeEndsAt);
				membershipLevel = hasPaid && userInfo.membershipId === MembershipID.Free ? "Onetime" : userInfo.membershipFormatted;
			}
			set({
				user: userInfo,
				hasPaid: hasPaid,
				creditsAll: userInfo ? userInfo.creditFree + userInfo.creditOneTime + userInfo.creditSubscription : 0,
				membershipLevel: membershipLevel,
			});
		},
	};
});
