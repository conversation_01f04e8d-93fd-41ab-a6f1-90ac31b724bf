import { MetadataRoute } from "next";
import { WEBHOST } from "@/lib/constants";

export const dynamic = "force-static";

export default function robots(request: Request): MetadataRoute.Robots {
	return {
		rules: {
			userAgent: "*",
			allow: "/",
			disallow: ["/_next/", "/api/", "/static/", "/404", "/500", "/*.json$", "/user", "/cdn-cgi", "/zdata"],
		},
		sitemap: `${WEBHOST}/sitemap.xml`,
		host: WEBHOST,
	};
}
