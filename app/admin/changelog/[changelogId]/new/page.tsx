import { WEBNAME } from "@/lib/constants";
import { Metadata } from "next";
import EditChangelogComponent from "../edit-changelog-component";

export const metadata: Metadata = {
	title: `New Changelog | ${WEBNAME}`,
	robots: {
		index: false,
		follow: false,
	},
};

type Params = Promise<{ changelogId: number }>;
export default async function Page({ params }: { params: Params }) {
	const { changelogId } = await params;

	return <EditChangelogComponent changelogId={changelogId} changelogItemId={null} changelog={null} />;
}
