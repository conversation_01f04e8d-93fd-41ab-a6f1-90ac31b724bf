import { notFound } from "next/navigation";
import { ChangelogTranslation, changelogTranslationSchema } from "@/server/db/schema.server";
import { getDB } from "@/server/db/db-client.server";
import { desc, eq } from "drizzle-orm";
import EditChangelogComponent from "../edit-changelog-component";
import { Metadata } from "next";
import { WEBNAME } from "@/lib/constants";

export const metadata: Metadata = {
	title: `Edit Changelog | ${WEBNAME}`,
	robots: {
		index: false,
		follow: false,
	},
};

async function getChangelog(id: number) {
	const db = getDB();
	const [changelog]: ChangelogTranslation[] = (await db
		.select()
		.from(changelogTranslationSchema)
		.where(eq(changelogTranslationSchema.id, id))
		.orderBy(desc(changelogTranslationSchema.id))) as ChangelogTranslation[];
	return changelog;
}

type Params = Promise<{ changelogId: number; id: number }>;
export default async function Page({ params }: { params: Params }) {
	const { changelogId, id } = await params;
	const changelog = await getChangelog(id);
	if (!changelog) {
		return notFound();
	}
	return <EditChangelogComponent changelogId={changelogId} changelogItemId={id} changelog={changelog} />;
}
