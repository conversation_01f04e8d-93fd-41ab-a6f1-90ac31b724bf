"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, Di<PERSON>Title, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { CalendarIcon } from "lucide-react";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import { toast } from "sonner";
import { SubmitButton } from "@/components/ui/custom/submit-button";
import { ofetch } from "ofetch";
import { ChangelogWithPartialTranslations } from "@/@types/admin/changelog/changelog";

interface ChangelogDialogProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	changelog?: ChangelogWithPartialTranslations | null;
	onSuccess?: () => void;
}

export default function ChangelogDialog({ open, onOpenChange, changelog, onSuccess }: ChangelogDialogProps) {
	const [isSubmitting, setIsSubmitting] = useState(false);
	const [version, setVersion] = useState<string>("");
	const [title, setTitle] = useState<string>("");
	const [image, setImage] = useState<string>("");
	const [publishedAt, setPublishedAt] = useState<Date | undefined>(new Date());

	// 重置表单
	const resetForm = () => {
		setVersion("");
		setTitle("");
		setImage("");
		setPublishedAt(new Date());
	};

	// 当dialog打开时，如果有changelog数据则填充表单，否则重置表单
	useEffect(() => {
		if (open) {
			if (changelog) {
				// 编辑模式
				setVersion(changelog.version || "");
				setTitle(changelog.title || "");
				setImage(changelog.image || "");
				setPublishedAt(changelog.publishedAt);
			} else {
				// 新增模式
				resetForm();
			}
		}
	}, [open, changelog]);

	const handleSubmit = async () => {
		if (isSubmitting) return;

		// 验证必填字段
		if (!version) {
			toast.error("Please enter a version");
			return;
		}

		if (!title.trim()) {
			toast.error("Please enter a title");
			return;
		}

		if (!publishedAt) {
			toast.error("Please select a publish date");
			return;
		}

		try {
			setIsSubmitting(true);

			const requestBody = {
				version: version.trim(),
				title: title.trim(),
				image: image.trim() || null,
				publishedAt: publishedAt,
				publishedAtChanged: changelog?.publishedAt !== publishedAt,
				hasTranslations: (changelog?.translations.length ?? 0) > 0,
				languages: changelog?.translations.map((t) => t.lang) || [],
				...(changelog && { id: changelog.id }), // 如果是编辑模式，包含id
			};

			const response = await ofetch("/api/admin/changelog", {
				method: "POST",
				body: requestBody,
			});

			if (response.status === 200) {
				toast.success(changelog ? "Changelog updated successfully" : "Changelog created successfully");
				onOpenChange(false);
				onSuccess?.();
			} else {
				toast.error(response.message || "Failed to save changelog");
			}
		} catch (error) {
			console.error("Error saving changelog:", error);
			toast.error("Failed to save changelog");
		} finally {
			setIsSubmitting(false);
		}
	};

	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent className="sm:max-w-[500px]">
				<DialogHeader>
					<DialogTitle>{changelog ? `Edit Changelog #${changelog.id}` : "New Changelog"}</DialogTitle>
				</DialogHeader>

				<div className="space-y-4 py-4">
					{/* Version */}
					<div className="space-y-2">
						<Label htmlFor="version">Version</Label>
						<Input id="version" value={version} onChange={(e) => setVersion(e.target.value)} placeholder="1.0" />
					</div>

					{/* Title */}
					<div className="space-y-2">
						<Label htmlFor="title">Title</Label>
						<Input id="title" value={title} onChange={(e) => setTitle(e.target.value)} placeholder="Enter changelog title" />
					</div>

					{/* Image URL */}
					<div className="space-y-2">
						<Label htmlFor="image">
							Image URL <span className="text-xs font-normal">(Optional)</span>
						</Label>
						<Input id="image" value={image} onChange={(e) => setImage(e.target.value)} placeholder="https://example.com/image.jpg" />
					</div>

					{/* Published Date */}
					<div className="space-y-2">
						<Label>Published Date</Label>
						<Popover>
							<PopoverTrigger asChild>
								<Button variant="outline" className={cn("w-full justify-start text-left font-normal", !publishedAt && "text-muted-foreground")}>
									<CalendarIcon className="mr-2 h-4 w-4" />
									{publishedAt ? format(publishedAt, "PPP") : <span>Pick a date</span>}
								</Button>
							</PopoverTrigger>
							<PopoverContent className="w-auto p-0" align="start">
								<Calendar mode="single" selected={publishedAt} onSelect={setPublishedAt} />
							</PopoverContent>
						</Popover>
					</div>
				</div>

				<DialogFooter>
					<Button variant="outline" onClick={() => onOpenChange(false)} disabled={isSubmitting}>
						Cancel
					</Button>
					<SubmitButton isSubmitting={isSubmitting} onClick={handleSubmit}>
						{changelog ? "Update" : "Create"}
					</SubmitButton>
				</DialogFooter>
			</DialogContent>
		</Dialog>
	);
}
