"use client";

import { Plus, Chevron<PERSON>eft, ChevronRight, RefreshCcw, Edit } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { useRouter } from "nextjs-toploader/app";
import { format } from "date-fns";
import { DashHeader } from "../_components/admin-navigate";
import { ChangelogStatus, ChangelogWithPartialTranslations } from "@/@types/admin/changelog/changelog";
import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import React from "react";
import { SubmitButton } from "@/components/ui/custom/submit-button";
import { Skeleton } from "@/components/ui/skeleton";
import ChangelogDialog from "./changelog-dialog";

interface PaginationInfo {
	page: number;
	pageSize: number;
	totalCount: number;
	totalPages: number;
}

export default function ChangelogHeadsComponent() {
	const router = useRouter();

	const [changelogs, setChangelogs] = useState<ChangelogWithPartialTranslations[]>([]);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);
	const [pagination, setPagination] = useState<PaginationInfo>({
		page: 1,
		pageSize: 10,
		totalCount: 0,
		totalPages: 0,
	});

	// Dialog state
	const [dialogOpen, setDialogOpen] = useState(false);
	const [editingChangelog, setEditingChangelog] = useState<ChangelogWithPartialTranslations | null>(null);

	// 获取changelog数据
	const fetchChangelogs = async (page: number = 1, pageSize: number = 10) => {
		try {
			setLoading(true);
			setError(null);

			const response = await fetch(`/api/admin/changelog?page=${page}&pageSize=${pageSize}`);
			const data = await response.json();

			if (data.status === 200) {
				setChangelogs(data.changelogs);
				setPagination(data.pagination);
			} else {
				setError(data.message || "Failed to fetch changelogs");
			}
		} catch (err) {
			setError("Failed to fetch changelogs");
			console.error("Error fetching changelogs:", err);
		} finally {
			setLoading(false);
		}
	};

	// 页面加载时获取数据
	useEffect(() => {
		fetchChangelogs();
	}, []);

	// 处理分页
	const handlePageChange = (newPage: number) => {
		if (newPage >= 1 && newPage <= pagination.totalPages) {
			fetchChangelogs(newPage, pagination.pageSize);
		}
	};

	// 处理新增changelog
	const handleNewChangelog = () => {
		setEditingChangelog(null);
		setDialogOpen(true);
	};

	// 处理编辑changelog
	const handleEditChangelog = (changelog: ChangelogWithPartialTranslations) => {
		setEditingChangelog(changelog);
		setDialogOpen(true);
	};

	// Dialog成功回调
	const handleDialogSuccess = () => {
		fetchChangelogs(pagination.page, pagination.pageSize);
	};
	// 渲染每个changelog卡片
	const renderChangelogCards = () => {
		return changelogs.map((changelog) => (
			<Card key={changelog.id} className="mb-4 gap-4 overflow-hidden py-0">
				<CardHeader className="bg-muted gap-0 border-b px-4 [.border-b]:py-3">
					<div className="flex items-center justify-between gap-2">
						<div className="flex items-center space-x-3">
							<Badge variant="outline" className="bg-input rounded font-normal">
								v{changelog.version}
							</Badge>
							<p className="hidden md:block">{changelog.title || "Untitled"}</p>
						</div>
						<div className="flex items-center space-x-2">
							<Button variant="ghost" size="sm" onClick={() => handleEditChangelog(changelog)} className="text-muted-foreground h-7 px-2">
								<Edit className="h-4 w-4" />
							</Button>
							<div className="text-muted-foreground text-sm">{format(changelog.publishedAt, "MM/dd/yyyy")}</div>
						</div>
					</div>
					<p className="mt-1.5 md:hidden">{changelog.title || "Untitled"}</p>
				</CardHeader>
				<CardContent className="px-4 pb-3">
					<div className="divide-border divide-y">
						{changelog.translations.map((translation) => (
							<div
								key={translation.id}
								className="group cursor-pointer space-y-1 py-1.5 md:space-y-0"
								onClick={() => {
									router.push(`/admin/changelog/${changelog.id}/${translation.id}?_=${new Date().getTime()}`);
								}}
							>
								<div className="flex items-center justify-between gap-3">
									<div className="flex items-center space-x-3">
										<Badge variant="outline" className="bg-muted rounded font-normal">
											{translation.lang.toUpperCase()}
										</Badge>
										<span className="group-hover:text-action hidden text-sm md:block">{translation.title}</span>
									</div>
									<div className="flex items-center space-x-2">
										{translation.status === ChangelogStatus.Published && (
											<Badge variant="secondary" className="rounded bg-green-50 font-normal text-green-600 hover:bg-green-50">
												Published
											</Badge>
										)}
										{translation.status === ChangelogStatus.Draft && (
											<Badge variant="secondary" className="bg-input rounded font-normal">
												Draft
											</Badge>
										)}
									</div>
								</div>
								<span className="text-sm text-zinc-300 group-hover:text-zinc-100 md:hidden">{translation.title}</span>
							</div>
						))}
					</div>
					<Button
						size="sm"
						onClick={() => {
							router.push(`/admin/changelog/${changelog.id}/new?&_=${new Date().getTime()}`);
						}}
						className="bg-action hover:bg-action/80 mt-4 h-7 font-normal"
					>
						<Plus className="h-4 w-4" />
						Add
					</Button>
				</CardContent>
			</Card>
		));
	};

	return (
		<div className="flex h-full w-full flex-col">
			<DashHeader back={[{ href: "/admin", title: "Admin" }]} current={{ title: "Changelog" }} />

			<div className="flex-1 overflow-auto">
				<div className="mb-auto w-full space-y-4 px-4 pt-4 text-start md:container">
					<div className="flex w-full flex-row items-center justify-between">
						<div className="text-muted-foreground text-sm">
							{loading ? "Loading..." : error ? `Error: ${error}` : `Total: ${pagination.totalCount} items`}
						</div>
						<div className="flex items-center space-x-2">
							<SubmitButton isSubmitting={loading} size="sm" variant="secondary" onClick={() => fetchChangelogs()} className="">
								<RefreshCcw /> Refresh
							</SubmitButton>
							<Button size="sm" onClick={handleNewChangelog} className="bg-action hover:bg-action/80">
								<Plus className="h-4 w-4" />
								New
							</Button>
						</div>
					</div>

					<div className="space-y-4">
						{loading ? (
							<div className="flex flex-col gap-4">
								{Array.from({ length: 3 }).map((_, index) => (
									<Skeleton key={index} className="h-[120px] w-full rounded-xl" />
								))}
							</div>
						) : error ? (
							<div className="py-8 text-center">
								<span className="text-red-500">Error: {error}</span>
							</div>
						) : changelogs.length === 0 ? (
							<div className="py-8 text-center">
								<span className="text-muted-foreground">No changelogs found</span>
							</div>
						) : (
							renderChangelogCards()
						)}
					</div>
				</div>
			</div>

			{/* 分页控件 */}
			{!loading && !error && (
				<div className="border-t py-2">
					<div className="flex items-center justify-between px-4 md:container">
						<div className="text-muted-foreground text-sm">
							Page {pagination.page} of {pagination.totalPages}
						</div>
						<div className="flex items-center space-x-2">
							<Button size="sm" onClick={() => handlePageChange(pagination.page - 1)} disabled={pagination.page <= 1}>
								<ChevronLeft className="h-4 w-4" />
								Previous
							</Button>
							<Button size="sm" onClick={() => handlePageChange(pagination.page + 1)} disabled={pagination.page >= pagination.totalPages}>
								Next
								<ChevronRight className="h-4 w-4" />
							</Button>
						</div>
					</div>
				</div>
			)}

			{/* Changelog Dialog */}
			<ChangelogDialog open={dialogOpen} onOpenChange={setDialogOpen} changelog={editingChangelog} onSuccess={handleDialogSuccess} />
		</div>
	);
}
