"use client";

import { ComponentProps } from "react";
import { House, Images, Logs, Rss, ToolCaseIcon, Video } from "lucide-react";
import { Sidebar, SidebarContent, SidebarHeader, SidebarMenuButton, SidebarRail } from "@/components/ui/sidebar";
import { NavMain } from "@/components/navigate/nav-main";
import { WEBNAME } from "@/lib/constants";
import { NoPrefetchLink } from "@/components/ui/custom/no-prefetch-link";

const data = {
	navMain: [
		{
			title: "Home",
			url: "/admin",
			icon: House,
		},
		// {
		// 	title: "Blog Tags",
		// 	url: "/admin/blog-tag",
		// 	icon: Tags,
		// },
		{
			title: "Blogs",
			url: "/admin/blog",
			icon: Rss,
		},
		{
			title: "Changelog",
			url: "/admin/changelog",
			icon: Logs,
		},
	],
};

export function AdminSidebar({ ...props }: ComponentProps<typeof Sidebar>) {
	return (
		<Sidebar collapsible="icon" {...props}>
			<SidebarHeader>
				<NoPrefetchLink href="/admin">
					<SidebarMenuButton size="lg" className="">
						<div className="text-sidebar-primary-foreground flex aspect-square size-8 items-center justify-center rounded-lg">
							<img src="/favicon.ico" className="h-8" alt={`${WEBNAME} - AI quiz generator`} />
						</div>
						<div className="grid flex-1 text-left text-xl leading-tight">
							<span className="truncate font-semibold tracking-wide">Admin</span>
						</div>
					</SidebarMenuButton>
				</NoPrefetchLink>
			</SidebarHeader>
			<SidebarContent>
				<NavMain items={data.navMain} />
			</SidebarContent>
			<SidebarRail />
		</Sidebar>
	);
}
