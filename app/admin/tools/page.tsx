"use client";

import { useState } from "react";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { encodeBase64Url, decodeBase64Url } from "@/lib/utils-url";
import { DashHeader } from "../_components/admin-navigate";
import { useCopyToClipboard } from "usehooks-ts";
import { toast } from "sonner";
import { ClipboardIcon } from "lucide-react";

export default function Page() {
	const [jsonInput, setJsonInput] = useState("");
	const [encodedResult, setEncodedResult] = useState("");
	const [encodedInput, setEncodedInput] = useState("");
	const [decodedResult, setDecodedResult] = useState("");

	const [copiedText, copy] = useCopyToClipboard();
	const onCopy = (text: string) => () => {
		copy(text)
			.then(() => {
				// toast.success("Prompt copied! Click generate to create a new image")
				toast.success("Prompt copied!");
			})
			.catch((error: any) => {
				toast.error("Failed to copy!", error);
			});
	};

	const handleEncode = () => {
		const jsonInputTrim = jsonInput.trim();
		if (!jsonInputTrim) return;
		try {
			// 验证输入是否为有效的JSON
			const parsedJson = JSON.parse(jsonInputTrim);
			const result = encodeBase64Url(JSON.stringify(parsedJson));
			setEncodedResult(result);
		} catch (error) {
			setEncodedResult("错误：请输入有效的JSON字符串");
		}
	};

	const handleDecode = () => {
		const encodedInputTrim = encodedInput.trim();
		if (!encodedInputTrim) return;
		try {
			const result = decodeBase64Url(encodedInputTrim);
			setDecodedResult(result);
		} catch (error) {
			setDecodedResult("错误：无法解码输入的字符串");
		}
	};

	return (
		<div className="flex h-full w-full flex-col">
			<DashHeader current={{ title: "Tools" }} />

			<div className="flex-1 overflow-y-auto">
				<div className="container mb-auto w-full space-y-4 px-6 pt-4 text-start">
					<div className="text-center">
						<h1 className="text-3xl font-bold">Base64 URL 编码/解码工具</h1>
						<p className="text-muted-foreground mt-2">JSON字符串编码和字符串解码工具</p>
					</div>

					<div className="flex w-full flex-col gap-8">
						{/* JSON编码功能 */}
						<div className="bg-muted flex flex-col space-y-8 rounded-2xl p-6">
							<div>
								<div className="text-xl font-medium">JSON字符串编码</div>
								<div className="text-muted-foreground text-sm">输入JSON字符串，将其编码为URL安全的Base64格式</div>
							</div>
							<div className="space-y-4">
								<div>
									<p className="mb-2 block text-sm font-medium">JSON输入</p>
									<Textarea
										id="json-input"
										placeholder='请输入JSON字符串，例如：{"name": "test", "value": 123}'
										value={jsonInput}
										onChange={(e) => setJsonInput(e.target.value)}
										className="min-h-28 bg-white"
									/>
								</div>
								<Button onClick={handleEncode} className="w-full">
									Encode
								</Button>
								<div>
									<p className="mb-2 block text-sm font-medium">Result</p>

									{encodedResult && (
										<div className="rounded-xl bg-white p-4">
											<p className="text-foreground w-full break-words whitespace-pre-wrap">{encodedResult}</p>
											<div className="flex w-full justify-end">
												<Button size="sm" variant="secondary" className="font-normal" onClick={onCopy(encodedResult)}>
													<ClipboardIcon /> Copy
												</Button>
											</div>
										</div>
									)}
								</div>
							</div>
						</div>

						{/* 字符串解码功能 */}

						<div className="bg-muted space-y-8 rounded-2xl p-6">
							<div>
								<div className="text-xl font-medium">Base64 URL字符串解码</div>
								<div className="text-muted-foreground text-sm">输入Base64 URL编码的字符串，将其解码为原始字符串</div>
							</div>
							<div className="space-y-4">
								<div>
									<p className="mb-2 block text-sm font-medium">编码字符串输入</p>
									<Textarea
										id="encoded-input"
										placeholder="请输入Base64 URL编码的字符串"
										value={encodedInput}
										onChange={(e) => setEncodedInput(e.target.value)}
										className="min-h-28 bg-white"
									/>
								</div>
								<Button onClick={handleDecode} className="w-full">
									Decode
								</Button>
								<div>
									<p className="mb-2 block text-sm font-medium">Result</p>

									{decodedResult && (
										<div className="rounded-xl bg-white p-4">
											<p className="text-foreground w-full break-words whitespace-pre-wrap">{decodedResult}</p>
											<div className="flex w-full justify-end">
												<Button size="sm" variant="secondary" className="font-normal" onClick={onCopy(decodedResult)}>
													<ClipboardIcon /> Copy
												</Button>
											</div>
										</div>
									)}
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
}
