"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON>Title, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Check, ChevronDownIcon, CircleXIcon, CloudUpload, LoaderCircleIcon, TrashIcon } from "lucide-react";
import { cn } from "@/lib/utils";
import { toast } from "sonner";
import { SubmitButton } from "@/components/ui/custom/submit-button";
import { ofetch } from "ofetch";
import { BlogPostWithPartialTranslations } from "@/@types/admin/blog/blog";
import { useBlogCategoryStore } from "@/store/admin/useBlogCategoryStore";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import urlSlug from "url-slug";
import { useDropzone } from "react-dropzone";
import { uploadFileAdmin } from "@/lib/file/upload-file";
import { AuthError } from "@/@types/error";
import { useSignInBoxOpenStore } from "@/store/useSignInBoxOpenStore";

interface BlogDialogProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	blog?: BlogPostWithPartialTranslations | null;
	onSuccess?: () => void;
}

export default function BlogDialog({ open, onOpenChange, blog, onSuccess }: BlogDialogProps) {
	const { setSignInBoxOpen } = useSignInBoxOpenStore();
	const { categories } = useBlogCategoryStore();

	const [isSubmitting, setIsSubmitting] = useState(false);
	const [slug, setSlug] = useState<string>("");
	const [title, setTitle] = useState<string>("");
	const [image, setImage] = useState<string>("");
	const [categoryId, setCategoryId] = useState<number | null>(null);

	// 重置表单
	const resetForm = () => {
		setSlug("");
		setTitle("");
		setImage("");
		setCategoryId(null);
	};

	// 当dialog打开时，如果有blog数据则填充表单，否则重置表单
	useEffect(() => {
		if (open) {
			if (blog) {
				// 编辑模式
				setSlug(blog.slug || "");
				setTitle(blog.title || "");
				setImage(blog.image || "");
				setCategoryId(blog.categoryId ?? null);
			} else {
				// 新增模式
				resetForm();
			}
		}
	}, [open, blog]);

	const [uploadingImage, setUploadingImage] = useState<boolean>(false);
	const {
		getRootProps: getRootPropsImage,
		getInputProps: getInputPropsImage,
		open: openDropzoneImage,
	} = useDropzone({
		noClick: true,
		multiple: false,
		accept: {
			"image/webp": [],
		},
		maxFiles: 1,
		onError: console.error,
		onDrop: async (acceptedFiles, fileRejections, event) => {
			if (!blog?.id) {
				toast.warning("Please save the blog first before uploading images.");
				return;
			}

			if (fileRejections.length > 0) {
				const message = fileRejections.at(0)?.errors.at(0)?.message;
				console.error?.(new Error(message));
				return;
			}

			if (!acceptedFiles || acceptedFiles.length === 0) return;
			let file = acceptedFiles[0];

			if (file.size > 4 * 1024 * 1024) {
				toast.warning("Image exceeds 4MB. Please upload a smaller one.");
				return;
			}

			try {
				setUploadingImage(true);
				const { file_url } = await uploadFileAdmin(file, {
					prefixType: "blog",
					subPath: `post-${blog.id}/lg`,
				});
				setImage(file_url);
			} catch (error: any) {
				console.error("Failed to upload image:", error.message);
				if (error instanceof AuthError) {
					setSignInBoxOpen(true);
					return;
				}
				toast.error(`Upload image failed: ${error.message}`);
			} finally {
				setUploadingImage(false);
			}
		},
	});

	const handleSubmit = async () => {
		if (isSubmitting) return;

		// 验证必填字段
		if (!slug) {
			toast.error("Please enter a slug");
			return;
		}

		if (!title.trim()) {
			toast.error("Please enter a title");
			return;
		}

		try {
			setIsSubmitting(true);

			const requestBody = {
				slug: slug.trim(),
				title: title.trim(),
				image: image.trim() || null,
				categoryId: categoryId ?? null,
				hasTranslations: (blog?.translations.length ?? 0) > 0,
				languages: blog?.translations.map((t) => t.lang) || [],
				...(blog && { id: blog.id }), // 如果是编辑模式，包含id
			};

			const response = await ofetch("/api/admin/blog", {
				method: "POST",
				body: requestBody,
			});

			if (response.status === 200) {
				toast.success(blog ? "Blog updated successfully" : "Blog created successfully");
				onOpenChange(false);
				onSuccess?.();
			} else {
				toast.error(response.message || "Failed to save blog");
			}
		} catch (error) {
			console.error("Error saving blog:", error);
			toast.error("Failed to save blog");
		} finally {
			setIsSubmitting(false);
		}
	};

	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent className="sm:max-w-[500px]">
				<DialogHeader>
					<DialogTitle>{blog ? `Edit Blog #${blog.id}` : "New Blog"}</DialogTitle>
				</DialogHeader>

				<div className="space-y-4 py-4">
					{/* Title */}
					<div className="space-y-2">
						<Label htmlFor="title">Title</Label>
						<Input
							id="title"
							value={title}
							onChange={(e) => {
								setTitle(e.target.value);
								if (blog?.id) return;
								if (e.target.value) {
									setSlug(urlSlug(e.target.value));
								} else {
									setSlug("");
								}
							}}
							placeholder="Enter blog title"
						/>
					</div>

					{/* Slug */}
					<div className="space-y-2">
						<Label htmlFor="slug">Slug</Label>
						<Input id="slug" value={slug} onChange={(e) => setSlug(e.target.value)} placeholder="Enter blog slug" />
					</div>

					<div className="space-y-2">
						<Label>Category</Label>
						<div className="flex flex-row items-center justify-between">
							<DropdownMenu>
								<DropdownMenuTrigger
									className={cn(
										"ring-offset-background flex h-9 w-full cursor-pointer flex-row items-center justify-between rounded-md border px-3 py-2 text-sm whitespace-nowrap shadow-none ring-offset-0",
										"focus:ring-ring data-placeholder:text-muted-foreground focus:ring-0 focus:outline-hidden disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",
									)}
								>
									<div className="flex flex-row items-center gap-2">{categories.find((category) => category.id === categoryId)?.name}</div>
									<ChevronDownIcon className="opacity-50" size={16} aria-hidden="true" />
								</DropdownMenuTrigger>
								<DropdownMenuContent className="min-w-(--radix-dropdown-menu-trigger-width)">
									{categories.map((categoryOption, index) => (
										<DropdownMenuItem
											key={index}
											className="flex cursor-pointer flex-row items-center justify-between gap-2 text-[13px] [&>svg]:size-[14px]"
											onClick={() => {
												setCategoryId(categoryOption.id!);
											}}
										>
											{categoryOption.name}
											{categoryOption.id === categoryId && <Check className="size-[14px]" />}
										</DropdownMenuItem>
									))}
								</DropdownMenuContent>
							</DropdownMenu>
							{categoryId && (
								<button className="text-destructive ml-2 text-sm" onClick={() => setCategoryId(null)}>
									<CircleXIcon className="size-4" />
								</button>
							)}
						</div>
					</div>

					{/* Image URL */}
					<div className="space-y-2">
						<Label>
							Image URL <span className="text-xs font-normal">(Optional)</span>
						</Label>
						{image ? (
							<div className="flex flex-col">
								<div className="relative h-[160px] overflow-hidden rounded-lg">
									<img src={image} alt={title} className="h-full w-full object-cover" />
									<Button
										size="icon"
										variant="secondary"
										className="text-destructive absolute top-2 right-2 rounded-full"
										onClick={() => {
											setImage("");
										}}
									>
										<TrashIcon className="" />
									</Button>
								</div>
								<p className="text-muted-foreground mt-0.5 text-center text-xs">{image}</p>
							</div>
						) : (
							<div>
								<div
									className={cn(
										"h-[160px] w-full flex-col justify-center rounded-lg border-2 border-dashed",
										blog?.id ? "hover:border-action/50 cursor-pointer" : "cursor-not-allowed opacity-50",
									)}
									{...getRootPropsImage()}
									onClick={() => {
										if (blog?.id) {
											openDropzoneImage();
										} else {
											toast.warning("Please save the blog first before uploading images.");
										}
									}}
								>
									{uploadingImage ? (
										<div className="text-muted-foreground flex h-full flex-col items-center justify-center gap-1 text-sm font-normal">
											<LoaderCircleIcon className="text-muted-foreground animate-spin" />
											<p>Uploading...</p>
										</div>
									) : (
										<div className="text-muted-foreground flex h-full flex-col items-center justify-center gap-1 text-sm font-normal">
											<CloudUpload />
											<p>{blog?.id ? "Click or drag image here" : "Save blog first to upload images"}</p>
										</div>
									)}
								</div>
								<input {...getInputPropsImage()} />
							</div>
						)}
					</div>
				</div>

				<DialogFooter>
					<Button variant="outline" onClick={() => onOpenChange(false)} disabled={isSubmitting}>
						Cancel
					</Button>
					<SubmitButton isSubmitting={isSubmitting} onClick={handleSubmit}>
						{blog ? "Update" : "Create"}
					</SubmitButton>
				</DialogFooter>
			</DialogContent>
		</Dialog>
	);
}
