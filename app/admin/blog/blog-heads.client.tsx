"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { ChevronLeft, ChevronRight, Edit, LinkIcon, Plus, RefreshCcw } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { useBlogCategoryStore } from "@/store/admin/useBlogCategoryStore";
import { useRouter } from "nextjs-toploader/app";
import { DashHeader } from "../_components/admin-navigate";
import { BlogPostWithPartialTranslations, BlogStatus } from "@/@types/admin/blog/blog";
import { useEffect, useState } from "react";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { SubmitButton } from "@/components/ui/custom/submit-button";
import { Skeleton } from "@/components/ui/skeleton";
import BlogDialog from "./blog-dialog";
import { NoPrefetchLink } from "@/components/ui/custom/no-prefetch-link";
import { WEBHOST } from "@/lib/constants";

interface PaginationInfo {
	page: number;
	pageSize: number;
	totalCount: number;
	totalPages: number;
}

export default function BlogHeadsComponent() {
	const router = useRouter();
	const { categories } = useBlogCategoryStore();

	const [blogPosts, setBlogPosts] = useState<BlogPostWithPartialTranslations[]>([]);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);
	const [pagination, setPagination] = useState<PaginationInfo>({
		page: 1,
		pageSize: 10,
		totalCount: 0,
		totalPages: 0,
	});

	// Dialog state
	const [dialogOpen, setDialogOpen] = useState(false);
	const [editingBlog, setEditingBlog] = useState<BlogPostWithPartialTranslations | null>(null);

	// 获取changelog数据
	const fetchBlogs = async (page: number = 1, pageSize: number = 10) => {
		try {
			setLoading(true);
			setError(null);

			const response = await fetch(`/api/admin/blog?page=${page}&pageSize=${pageSize}`);
			const data = await response.json();

			if (data.status === 200) {
				setBlogPosts(data.blogPosts);
				setPagination(data.pagination);
			} else {
				setError(data.message || "Failed to fetch blogs");
			}
		} catch (err) {
			setError("Failed to fetch blogs");
			console.error("Error fetching blogs:", err);
		} finally {
			setLoading(false);
		}
	};

	// 页面加载时获取数据
	useEffect(() => {
		fetchBlogs();
	}, []);

	// 处理分页
	const handlePageChange = (newPage: number) => {
		if (newPage >= 1 && newPage <= pagination.totalPages) {
			fetchBlogs(newPage, pagination.pageSize);
		}
	};

	// 处理新增blog
	const handleNewBlogPost = () => {
		setEditingBlog(null);
		setDialogOpen(true);
	};

	// 处理编辑blog
	const handleEditBlog = (changelog: BlogPostWithPartialTranslations) => {
		setEditingBlog(changelog);
		setDialogOpen(true);
	};

	// Dialog成功回调
	const handleDialogSuccess = () => {
		fetchBlogs(pagination.page, pagination.pageSize);
	};
	// blog
	const renderBlogPostCards = () => {
		return blogPosts.map((blogPost) => (
			<Card key={blogPost.id} className="mb-4 gap-4 overflow-hidden py-0">
				<CardHeader className="bg-muted gap-0 border-b px-4 [.border-b]:py-3">
					<div className="flex items-start justify-between gap-2">
						<div className="flex items-start space-x-3">
							{blogPost.categoryId && (
								<Badge variant="outline" className="bg-input rounded font-normal">
									{categories.find((category) => category.id === blogPost.categoryId)?.name}
								</Badge>
							)}
							<div className="hidden md:block">
								<p>{blogPost.title || "Untitled"}</p>
								{blogPost.image && <img src={blogPost.image} className="h-16 rounded-sm" />}
							</div>
						</div>
						<div className="flex items-center space-x-2">
							<Button variant="ghost" size="sm" onClick={() => handleEditBlog(blogPost)} className="h-7 px-2">
								<Edit className="h-4 w-4" />
							</Button>
							{/* <div className="text-muted-foreground text-sm">{format(blogPost.publishedAt, "MM/dd/yyyy")}</div> */}
						</div>
					</div>
					<div className="mt-1.5 md:hidden">
						<p>{blogPost.title || "Untitled"}</p>
						{blogPost.image && <img src={blogPost.image} className="h-16 rounded-sm" />}
					</div>
				</CardHeader>
				<CardContent className="px-4 pb-3">
					<div className="divide-border divide-y">
						{blogPost.translations.map((translation) => (
							<div
								key={translation.id}
								className="group cursor-pointer space-y-1 py-1.5 md:space-y-0"
								onClick={() => {
									router.push(`/admin/blog/${blogPost.id}/${translation.id}?_=${new Date().getTime()}`);
								}}
							>
								<div className="flex items-center justify-between gap-3">
									<div className="flex items-center space-x-3">
										<Badge variant="outline" className="bg-muted rounded font-normal">
											{translation.lang.toUpperCase()}
										</Badge>
										<span className="hover:text-action hidden text-sm md:block">{translation.title}</span>
									</div>
									<div className="flex items-center space-x-2">
										{translation.status === BlogStatus.Published && (
											<>
												<NoPrefetchLink href={`${WEBHOST}/blog/${blogPost.slug}`} target="_blank" className="text-xs hover:underline">
													<LinkIcon className="h-4 w-4" />
												</NoPrefetchLink>

												<Badge variant="secondary" className="rounded bg-green-50 font-normal text-green-600 hover:bg-green-50">
													Published
												</Badge>
											</>
										)}
										{translation.status === BlogStatus.Draft && (
											<Badge variant="secondary" className="bg-input rounded font-normal">
												Draft
											</Badge>
										)}
									</div>
								</div>
								<span className="text-sm text-zinc-300 group-hover:text-zinc-100 md:hidden">{translation.title}</span>
							</div>
						))}
					</div>
					<Button
						size="sm"
						onClick={() => {
							router.push(`/admin/blog/${blogPost.id}/new?&_=${new Date().getTime()}`);
						}}
						className="bg-action hover:bg-action/80 mt-4 h-7 font-normal"
					>
						<Plus className="h-4 w-4" />
						Add
					</Button>
				</CardContent>
			</Card>
		));
	};

	return (
		<div className="flex h-full w-full flex-col">
			<DashHeader back={[{ href: "/admin", title: "Admin" }]} current={{ title: "Blogs" }} />

			<div className="flex-1 overflow-auto">
				<div className="mb-auto w-full space-y-4 px-4 pt-4 text-start md:container">
					<div className="flex w-full flex-row items-center justify-between">
						<div className="text-muted-foreground text-sm">{loading ? "Loading..." : error ? "" : `Total: ${pagination.totalCount} items`}</div>
						<div className="flex items-center space-x-2">
							<SubmitButton isSubmitting={loading} size="sm" variant="secondary" onClick={() => fetchBlogs()} className=" ">
								<RefreshCcw /> Refresh
							</SubmitButton>
							<Button size="sm" onClick={handleNewBlogPost} className="bg-action hover:bg-action/80">
								<Plus className="h-4 w-4" />
								New
							</Button>
						</div>
					</div>

					<div className="space-y-4">
						{loading ? (
							<div className="flex flex-col gap-4">
								{Array.from({ length: 3 }).map((_, index) => (
									<Skeleton key={index} className="h-[120px] w-full rounded-xl" />
								))}
							</div>
						) : error ? (
							<div className="py-8 text-center">
								<span className="text-red-500">Error: {error}</span>
							</div>
						) : blogPosts.length === 0 ? (
							<div className="py-8 text-center">
								<span className="text-muted-foreground">No blog found</span>
							</div>
						) : (
							renderBlogPostCards()
						)}
					</div>
				</div>
			</div>

			{/* 分页控件 */}
			{!loading && !error && (
				<div className="border-t py-2">
					<div className="flex items-center justify-between px-4 md:container">
						<div className="text-muted-foreground text-sm">
							Page {pagination.page} of {pagination.totalPages}
						</div>
						<div className="flex items-center space-x-2">
							<Button size="sm" onClick={() => handlePageChange(pagination.page - 1)} disabled={pagination.page <= 1}>
								<ChevronLeft className="h-4 w-4" />
								Previous
							</Button>
							<Button size="sm" onClick={() => handlePageChange(pagination.page + 1)} disabled={pagination.page >= pagination.totalPages}>
								Next
								<ChevronRight className="h-4 w-4" />
							</Button>
						</div>
					</div>
				</div>
			)}

			{/* Blog Dialog */}
			<BlogDialog open={dialogOpen} onOpenChange={setDialogOpen} blog={editingBlog} onSuccess={handleDialogSuccess} />
		</div>
	);
}
