import EditBlogComponent from "../edit-blog-component";
import { WEBNAME } from "@/lib/constants";
import { Metadata } from "next";

export const metadata: Metadata = {
	title: `New Blog | ${WEBNAME}`,
	robots: {
		index: false,
		follow: false,
	},
};

type Params = Promise<{ blogId: number }>;
export default async function Page({ params }: { params: Params }) {
	const { blogId } = await params;

	return <EditBlogComponent blogId={blogId} blogItemId={null} blog={null} />;
}
