import { notFound } from "next/navigation";
import { BlogPostTranslation, blogPostTranslationSchema } from "@/server/db/schema.server";
import { getDB } from "@/server/db/db-client.server";
import { desc, eq } from "drizzle-orm";
import EditBlogComponent from "../edit-blog-component";
import { WEBNAME } from "@/lib/constants";
import { Metadata } from "next";

async function getBlogPostTranslation(id: number) {
	const db = getDB();
	const [blogPost]: BlogPostTranslation[] = (await db
		.select()
		.from(blogPostTranslationSchema)
		.where(eq(blogPostTranslationSchema.id, id))
		.orderBy(desc(blogPostTranslationSchema.id))) as BlogPostTranslation[];
	return blogPost;
}

export const metadata: Metadata = {
	title: `Edit Blog | ${WEBNAME}`,
	robots: {
		index: false,
		follow: false,
	},
};

type Params = Promise<{ blogId: number; id: number }>;
export default async function Page({ params }: { params: Params }) {
	const { blogId, id } = await params;
	const blogPost = await getBlogPostTranslation(id);
	if (!blogPost) {
		return notFound();
	}
	return <EditBlogComponent blogId={blogId} blogItemId={id} blog={blogPost} />;
}
