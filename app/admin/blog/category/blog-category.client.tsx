"use client";

import { useEffect, useState } from "react";
import { <PERSON><PERSON>, buttonVariants } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Edit, Plus } from "lucide-react";
import { toast } from "sonner";
import { SubmitButton } from "@/components/ui/custom/submit-button";
import { ofetch } from "ofetch";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { handleError } from "@/@types/error";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { useBlogCategoryStore } from "@/store/admin/useBlogCategoryStore";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { blogCategoryTypeSchema, BlogCategorySchemaType } from "@/@types/admin/blog/blog";
import { cn } from "@/lib/utils";
import urlSlug from "url-slug";
import { DashHeader } from "../../_components/admin-navigate";

export default function BlogCategoryComponent() {
	const { categories, refreshCategories } = useBlogCategoryStore();

	const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
	const [isEditCategoryLoading, setIsEditCategoryLoading] = useState(false);
	const form = useForm<BlogCategorySchemaType>({
		resolver: zodResolver(blogCategoryTypeSchema),
		defaultValues: {
			name: "",
			slug: "",
		},
	});
	const categoryName = form.watch("name");

	const onSubmit = async (data: BlogCategorySchemaType) => {
		if (isEditCategoryLoading) return;

		try {
			setIsEditCategoryLoading(true);

			const { status, message } = await ofetch("/api/admin/blog-category", {
				method: "POST",
				body: data,
			});
			handleError(status, message);
			await refreshCategories();
			setIsEditDialogOpen(false);
			form.reset({
				name: "",
				slug: "",
			});
			if (data.id) {
				toast.success("Category updated successfully");
			} else {
				toast.success("Category added successfully");
			}
		} catch (error) {
			toast.error("An error occurred");
		} finally {
			setIsEditCategoryLoading(false);
		}
	};

	useEffect(() => {
		if (!categoryName) {
			form.setValue("slug", "");
		} else {
			form.setValue("slug", urlSlug(categoryName));
		}
	}, [categoryName]);

	return (
		<div className="flex h-full w-full flex-col">
			<DashHeader back={[{ href: "/admin", title: "Admin" }]} current={{ title: "Blog Category" }} />

			<div className="flex-1 overflow-auto">
				<div className="mb-auto w-full space-y-4 pt-4 text-start md:container">
					<div className="flex w-full flex-row justify-end">
						<Button
							variant="outline"
							onClick={() => {
								form.reset({
									name: "",
									slug: "",
								});
								setIsEditDialogOpen(true);
							}}
						>
							<Plus className="mr-1 h-4 w-4" />
							New
						</Button>
					</div>

					<Table>
						<TableHeader className="[&_tr]:border-b-0">
							<TableRow className="bg-muted">
								<TableHead className="font-normal"></TableHead>
								<TableHead className="font-normal">Name</TableHead>
								<TableHead className="font-normal">slug</TableHead>
								<TableHead className="font-normal"></TableHead>
							</TableRow>
						</TableHeader>
						<TableBody>
							{categories.map((category, index) => (
								<TableRow key={category.id}>
									<TableCell className="text-muted-foreground w-8 py-4">
										<span className="text-sm">{index + 1}</span>
									</TableCell>
									<TableCell className="font-medium">
										<span className="text-sm">{category.name}</span>
									</TableCell>
									<TableCell className="mx-auto">
										<span className="text-sm">{category.slug}</span>
									</TableCell>
									<TableCell className="mx-auto">
										<Button
											variant="ghost"
											size="icon"
											onClick={() => {
												form.reset({
													id: category.id,
													name: category.name,
												});
												setIsEditDialogOpen(true);
											}}
										>
											<Edit className="h-4 w-4" />
										</Button>
									</TableCell>
								</TableRow>
							))}
						</TableBody>
					</Table>
				</div>

				<Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
					<DialogContent>
						<DialogHeader>
							<DialogTitle>Category</DialogTitle>
						</DialogHeader>
						<Form {...form}>
							<form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
								<FormField
									control={form.control}
									name="name"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Name *</FormLabel>
											<FormControl>
												<Input placeholder="Enter name" {...field} />
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
								<FormField
									control={form.control}
									name="slug"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Slug *</FormLabel>
											<FormControl>
												<Input placeholder="Enter Slug" {...field} />
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
								<div className="flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2">
									<p
										className={cn(buttonVariants({ variant: "outline" }), "cursor-pointer")}
										onClick={() => {
											form.reset({});
											setIsEditDialogOpen(false);
										}}
									>
										Cancel
									</p>
									<SubmitButton isSubmitting={isEditCategoryLoading} type="submit">
										Save
									</SubmitButton>
								</div>
							</form>
						</Form>
					</DialogContent>
				</Dialog>
			</div>
		</div>
	);
}
