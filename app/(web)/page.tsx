import type { Metadata } from "next";
import { OSS_URL_HOST, WEBNAME } from "@/lib/constants";
import FAQsComponent from "@/components/landing/faqs";
import FinalCTA from "@/components/landing/final-cta";
import EditHomeClient from "@/components/app/edit-home.client";
import { getCurrentSessionUser } from "@/server/auth/auth-session";
import { redirect } from "next/navigation";
import FeaturesComponent from "@/components/landing/features";

export const metadata: Metadata = {
	title: `AI Image Editor: Your AI Image Editing Pal | ${WEBNAME}`,
	description:
		"Image editing is now as simple as describing your idea. Our AI image editor creates flawless results while maintaining perfect consistency for any subject.",
	alternates: {
		canonical: "/",
	},
};

export default async function Page() {
	const sessionUser = await getCurrentSessionUser();
	if (process.env.NODE_ENV === "production" && sessionUser) redirect("/edit");

	return (
		<main className="min-h-screen pb-24">
			<div className="mb-18 h-[calc(100vh-64px)] px-4 pb-6 md:px-6">
				<div className="your_css_selector_here container flex h-full flex-col justify-center rounded-2xl p-4 md:rounded-4xl">
					<div className="flex w-full grow flex-col items-center justify-center md:grow-0">
						<div className="mx-auto flex w-full flex-col items-center gap-y-4 text-center">
							<h1 className="text-[32px] leading-9 font-medium md:text-[40px] md:leading-16 md:font-semibold">
								{/* Your AI Image Editing Pal */}
								AI Image Editor That Works Like a Pal
							</h1>
							<div className="text-muted-foreground text-base leading-none">
								{/* Edit and refine your images, maintaining consistency with simple prompts. */}
								The first image editor with true creative control and flawless consistency.
							</div>
						</div>
						<div className="mt-10 w-full md:mt-14">
							<EditHomeClient />
						</div>
					</div>
					{/* <div className="mt-2 flex w-full justify-center gap-x-2 overflow-hidden">
						<div className="text-primary bg-muted flex h-8 items-center justify-center rounded-2xl px-4 py-2 whitespace-nowrap backdrop-blur-[10px]">
							<span className="text-xs font-medium">Remove the background</span>
						</div>
						<div className="text-primary flex h-8 items-center justify-center rounded-2xl bg-white/40 px-4 py-2 whitespace-nowrap backdrop-blur-[10px]">
							<span className="text-xs font-medium">Remove the background</span>
						</div>
						<div className="text-primary flex h-8 items-center justify-center rounded-2xl bg-white/40 px-4 py-2 whitespace-nowrap backdrop-blur-[10px]">
							<span className="text-xs font-medium">Remove the background</span>
						</div>
					</div> */}
				</div>
			</div>

			<div className="py-24">
				<div className="container flex flex-col items-center gap-12 px-4 md:px-6">
					<div className="text-center">
						<h2 className="text-3xl font-semibold text-pretty">The AI image editor that understands your words</h2>
						<div className="text-secondary-foreground mx-auto mt-8 flex max-w-4xl flex-col gap-4 text-start">
							<p>Stop wrestling with complex tools. Simply describe the edit you want, and watch your AI pal make it happen.</p>
							<p>
								<span className="font-semibold">Consistent characters, endless possibilities: </span>Place the same person or object in any
								scene. Editpal keeps every detail consistent, from one image to the next.
							</p>
							<p>
								<span className="font-semibold">Editing as easy as talking: </span>Want to change a background, adjust a pose, or add a hat?
								Just type it. No technical skills needed—if you can text, you can edit.
							</p>
							<p>
								<span className="font-semibold">Combine images seamlessly: </span>Merge elements from different photos into one perfect picture.
								Our AI ensures everything blends together naturally and logically.
							</p>
						</div>
					</div>
				</div>
			</div>

			{/* <GridSections
				title="Unlock the Power of Unique AI-Designed Voices"
				description="Design unique, lifelike voices in seconds by simply describing them—no voice talent or equipment needed."
				features={[
					{
						title: "Hyper-Realistic Voice Design",
						description: "Create lifelike voices with unmatched clarity and expression that bring your ideas to life instantly.",
						icon: WandIcon,
					},
					{
						title: "Customizable Voice Creation",
						description: "Generate voices with personalized traits like tone, accent, gender, and age by simply describing what you need.",
						icon: Settings2Icon,
					},
					{
						title: "Instant Voice Generation",
						description: "Produce professional-quality voices in seconds, perfect for quick prototyping or creative exploration.",
						icon: ClockIcon,
					},
					{
						title: "No Voice Talent Needed",
						description: "Design voices without hiring voice actors or setting up complex studios—just describe and create.",
						icon: LayersIcon,
					},
					{
						title: "Multi-Language Support",
						description: "Generate voices in 20+ languages and accents, reaching global audiences with ease.",
						icon: GlobeIcon,
					},
					{
						title: "Simple Voice Design Interface",
						description: "Design unique voices with ease, no technical expertise or voice acting skills required.",
						icon: SmileIcon,
					},
				]}
			/> */}

			<FeaturesComponent
				ctaText="Start editing"
				ctaUrl="/edit"
				features={[
					{
						title: "Edit images with just a text prompt",
						description:
							"No more struggling with complicated editing tools. With Editpal, your AI image editor, all you need is a simple text prompt. Just type what you want—“change the background to a sunset,” “make the shirt blue,” or “remove the extra person”—and watch it happen instantly. Fast, effortless, and made for everyone.",
						image: `${OSS_URL_HOST}/mkt/home/<USER>
						imageAlt: `${WEBNAME}: Edit images with just a text prompt`,
					},
					{
						title: "Keep characters consistent every time",
						description:
							"Say goodbye to mismatched faces or details. Editpal keeps your characters, objects, and scenes consistent across every edit. Whether it’s the same person in multiple outfits, products shown at different angles, or characters in a storyboard—your AI image editor makes sure they always look like themselves.",
						image: `${OSS_URL_HOST}/mkt/home/<USER>
						imageAlt: `${WEBNAME}: Keep characters consistent every time`,
					},
					{
						title: "Precise, targeted edits made easy",
						description:
							"Need small, detailed changes without affecting the whole picture? Editpal gives you pinpoint control. Adjust a pose, brighten the colors, remove unwanted items, or fine-tune details—all with natural language commands. Perfect for when you want accuracy and creativity to go hand in hand.",
						image: `${OSS_URL_HOST}/mkt/home/<USER>
						imageAlt: `${WEBNAME}: Precise, targeted edits made easy`,
					},
					{
						title: "Merge and combine multiple images",
						description:
							"Bring your ideas together with seamless multi-image fusion. Editpal understands and blends several images into one unified design—without awkward edges or inconsistencies. From creative collages to professional marketing visuals, your AI image editor helps you build something that feels whole and natural.",
						image: `${OSS_URL_HOST}/mkt/home/<USER>
						imageAlt: `${WEBNAME}: Merge and combine multiple images`,
					},
					// {
					// 	title: "Transform styles in seconds",
					// 	description:
					// 		"Want to see your product in a retro look, a modern aesthetic, or even a hand-painted style? With Editpal, your AI image editor, you can instantly change the mood and design of any image. It’s your shortcut to unlimited creativity—test new concepts, explore bold looks, and find the style that fits your vision.",
					// 	image: `${OSS_URL_HOST}/mkt/home/<USER>
					// 	imageAlt: `${WEBNAME}: Transform styles in seconds`,
					// },
				]}
			/>

			{/* <HowToUse
				title=""
				steps={[
					{
						title: "",
						description: "",
					},
				]}
			/> */}
			<div className="py-24">
				<div className="container flex flex-col items-center gap-12 px-4 md:px-6">
					<div className="text-center">
						<h2 className="text-3xl font-semibold text-pretty">How to Edit Any Image with Editpal</h2>
						<div className="text-secondary-foreground mx-auto mt-8 flex max-w-4xl flex-col gap-4 text-start">
							<p>
								<span className="font-semibold">1. Upload your image: </span>
								Start by uploading one or more images you want to transform. Whether it’s a product photo, a portrait, or a quick sketch, our AI
								image editor understands them all and keeps every detail intact.
							</p>
							<p>
								<span className="font-semibold">2. Describe Your Vision: </span>
								Simply type what you want to see—change the background, adjust colors, add new elements, or blend multiple images into one. No
								editing skills needed; just use plain language and let your creativity flow.
							</p>
							<p>
								<span className="font-semibold">3. Click Generate & See the Magic: </span>Hit the generate button and watch your ideas come to
								life. Our AI image editor instantly delivers stunning, realistic results while keeping characters, and objects consistent across
								every edit.
							</p>
						</div>
					</div>
				</div>
			</div>

			{/* <div className="py-20">
				<div className="container flex flex-col items-center justify-center gap-12 px-6">
					<div className="max-w-4xl text-center">
						<h2 className="text-4xl font-semibold text-pretty">Plans for Every Need</h2>
						<p className="text-muted-foreground mx-auto mt-4 max-w-4xl text-pretty">Choose the plan that best fits your needs.</p>
					</div>

					<div className="w-full">
						<Plans />
					</div>
				</div>
			</div> */}

			<FAQsComponent
				title="Editpal image editor FAQs"
				faqs={[
					{
						question: "What is Editpal AI Image Editor?",
						answer: "Editpal is your friendly AI image editing pal that lets you transform and edit images just by typing what you want. No design skills? No problem. Simply describe your vision in plain words, and Editpal turns it into reality—whether that’s changing a background, adjusting colors, or keeping the same character consistent across multiple scenes. It’s like having a professional editor in your pocket, available anytime.",
					},
					{
						question: "Is the AI Image Editor free to use?",
						answer: "Yes, you can try our AI image editor for free with a limited number of edits, so you can experience how powerful and easy it is to use. If you’d like more edits, higher resolution outputs, and commercial usage rights, you can switch to one of our paid plans anytime.",
					},
					{
						question: "How does the AI Image Editor work?",
						answer: "It’s beautifully simple. You upload an image (or multiple images) and type a natural language instruction—like “replace the background with a sunny beach” or “make the person wear a red dress.” The AI processes your request and edits the image exactly as described, while preserving faces, objects, and details naturally. No layers, no complex tools—just your words.",
					},
					{
						question: "How is this different from traditional image editing software?",
						answer: "Traditional software like Photoshop requires manual work with brushes, layers, and masks—often taking hours and advanced skills. Our AI image editor works from plain text instructions, automating complex tasks in seconds. Plus, it can do things that are almost impossible manually, like keeping a character’s face identical across dozens of images or merging multiple photos seamlessly.",
					},
					{
						question: "What kinds of edits can I make?",
						answer: "The possibilities are almost endless. You can remove or add objects, swap backgrounds, change colors or styles, adjust lighting, modify a person’s pose, insert text, merge multiple images into one scene, or even turn a hand-drawn sketch into a clean instructional diagram. Whether you need subtle retouching or major transformations, the AI image editor can handle it.",
					},
					{
						question: "What are the benefits of using an AI Image Editor?",
						answer: "Speed, creativity, and freedom. You can achieve professional-looking edits in minutes without years of editing experience. It saves you hours of manual work, helps you create consistent visuals for branding, enables fast A/B testing for campaigns, and makes creative experimentation effortless. Plus, with Editpal’s intelligent image understanding, your edits look natural, not artificial.",
					},
					{
						question: "Which AI Image Editor is the best?",
						answer: "If you’re looking for an editor that understands your words, keeps characters consistent, merges multiple images seamlessly, and works in seconds—Editpal is hard to beat. It’s designed for creators, marketers, educators, and anyone who wants high-quality results without the learning curve.",
					},
					{
						question: "Can I use my edited images commercially?",
						answer: "Yes—if you’re on a paid plan, you have the rights to use your AI-edited images commercially. That means you can use them in ads, marketing materials, product listings, social media, or any professional project without worry.",
					},
				]}
			/>

			<FinalCTA
				className=""
				title="Image Editing Made Effortless"
				description="With just a simple prompt, AI instantly transforms your image, handling complex tasks like background swaps, pose adjustments, and more — all while keeping every detail flawless."
				ctaText="Start editing for free"
				ctaTextDisplay={true}
				ctaUrl="#"
			/>
		</main>
	);
}
