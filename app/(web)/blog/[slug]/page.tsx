import { i18nConfig } from "@/i18n-config";
import { notFound } from "next/navigation";
// import remarkGfm from "remark-gfm";
// import remarkMath from "remark-math";
// import { MemoizedReactMarkdown } from "@/components/markdown";
import { format } from "date-fns";
import { Badge } from "@/components/ui/badge";
import { Breadcrumb, BreadcrumbItem, BreadcrumbList, BreadcrumbSeparator } from "@/components/ui/breadcrumb";
import TocComponent from "./toc-component";
import { WEBNAME } from "@/lib/constants";
import { getBlogWithCategory } from "@/server/utils-blog.server";
import Link from "next/link";

type Params = Promise<{ lang: string; slug: string }>;

export const generateMetadata = async ({ params }: { params: Params }) => {
	const { lang, slug } = await params;
	const { blog } = await getBlogWithCategory(lang || i18nConfig.defaultLocale, slug);
	let prefixLang = "";
	if (lang && lang !== i18nConfig.defaultLocale) {
		prefixLang = `/${lang}`;
	}
	const canonicalPath = `${prefixLang}/blog/${slug}`;
	if (!blog) {
		return {
			title: `${WEBNAME} Blog`,
			description: "",
			alternates: {
				canonical: canonicalPath,
			},
		};
	}
	return {
		title: `${blog.metaTitle ?? blog.title} | ${WEBNAME} Blog`,
		description: blog.metaDescription ?? blog.intro,
		alternates: {
			canonical: canonicalPath,
		},
	};
};

export default async function Page({ params }: { params: Params }) {
	const { lang, slug } = await params;
	const { blog, blogCategories } = await getBlogWithCategory(lang || i18nConfig.defaultLocale, slug);
	if (!blog) {
		return notFound();
	}
	let categoryName: string | null = null;
	if (blog.categoryId) {
		categoryName = blogCategories.find((category: any) => category.id === blog.categoryId)?.name;
	}
	// const toc = extractTableOfContents(blog.markdown);
	// const toc = extractTableOfContentsFromHtml(blog.html);
	// console.log("toc:", toc);

	// const hasToc = blog.html.includes("<h2");

	return (
		<main className="flex min-h-screen w-full flex-col">
			<div className="container flex flex-col gap-6 pt-12 pb-12">
				<Breadcrumb>
					<BreadcrumbList className="flex-nowrap text-base">
						<BreadcrumbItem>
							<Link href="/blog" className="hover:text-foreground text-sm transition-colors hover:underline">
								Home
							</Link>
						</BreadcrumbItem>
						<BreadcrumbSeparator />
						<BreadcrumbItem>
							<Link
								href={`/blog/${blog.slug}`}
								className="text-action hover:text-action/80 line-clamp-1 text-sm text-ellipsis transition-colors hover:underline"
							>
								{blog.title}
							</Link>
						</BreadcrumbItem>
					</BreadcrumbList>
				</Breadcrumb>
				<div className="flex flex-col gap-4 md:gap-6">
					<h1 className="text-4xl font-bold text-pretty">{blog.title}</h1>
					<p className="text-muted-foreground">{blog.intro}</p>
					<div className="flex items-center space-x-2 text-sm">
						{categoryName && (
							<div className="flex flex-wrap gap-2">
								<Badge className="border-action bg-action-foreground text-action rounded-full font-normal shadow-none">{categoryName}</Badge>
							</div>
						)}
						<span className="text-muted-foreground text-xs">{format(blog.publishedAt, "MMM d, yyyy")}</span>
					</div>
				</div>
			</div>

			<div className="container flex space-x-6 pb-20 lg:space-x-12">
				<TocComponent html={blog.html!} />
				<div className="w-full">
					{/* <div className={cn("w-full md:pr-8", hasToc && "md:w-3/4")}> */}
					{blog.image && (
						<div className="aspect-[16/9] w-full overflow-hidden">
							<img src={blog.image} alt={blog.title} className="h-full w-full rounded-md object-cover" loading="lazy" />
						</div>
					)}
					<div
						className="prose-headings:font-title font-default prose dark:prose-invert mt-10 max-w-full focus:outline-hidden"
						dangerouslySetInnerHTML={{ __html: blog.html ?? "" }}
					/>
					{/* <MemoizedReactMarkdown
						className="prose max-w-none break-words dark:prose-invert prose-p:leading-relaxed prose-pre:p-0"
						remarkPlugins={[remarkGfm, remarkMath]}
						components={{
							p({ children }) {
								return <p className="mb-6 last:mb-0">{children}</p>;
							},
							h1: ({ children }) => (
								<h1 id={children?.toString().toLowerCase().replace(/\s+/g, "-")} className="-mt-16 pt-16">
									{children}
								</h1>
							),
							h2: ({ children }) => (
								<h2 id={children?.toString().toLowerCase().replace(/\s+/g, "-")} className="-mt-16 pt-16">
									{children}
								</h2>
							),
							h3: ({ children }) => (
								<h3 id={children?.toString().toLowerCase().replace(/\s+/g, "-")} className="-mt-16 pt-16">
									{children}
								</h3>
							),
							h4: ({ children }) => (
								<h4 id={children?.toString().toLowerCase().replace(/\s+/g, "-")} className="-mt-16 pt-16">
									{children}
								</h4>
							),
							h5: ({ children }) => (
								<h5 id={children?.toString().toLowerCase().replace(/\s+/g, "-")} className="-mt-16 pt-16">
									{children}
								</h5>
							),
							h6: ({ children }) => (
								<h6 id={children?.toString().toLowerCase().replace(/\s+/g, "-")} className="-mt-16 pt-16">
									{children}
								</h6>
							),
						}}
					>
						{blog.markdown}
					</MemoizedReactMarkdown> */}
				</div>
			</div>

			{/* <FinalCTA /> */}
		</main>
	);
}
