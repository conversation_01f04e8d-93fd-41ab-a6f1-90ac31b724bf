"use client";

import { cn } from "@/lib/utils";
import { AlignLeft } from "lucide-react";
import { useEffect, useRef, useState } from "react";

const extractTableOfContentsFromHtml = (html: string) => {
	// 创建一个临时的 DOM 元素来解析 HTML
	const doc = new DOMParser().parseFromString(html, "text/html");
	const headings = doc.querySelectorAll("h2, h3, h4, h5, h6");
	const toc: { title: string; id: string; level: number }[] = [];

	headings.forEach((heading) => {
		// console.log("heading:", heading);
		const level = parseInt(heading.tagName[1]);
		// const level = parseInt(heading.tagName.substring(1), 10);
		const title = heading.textContent || "";

		// 如果元素没有 id，生成一个
		let id = heading.id;
		if (!id) {
			id = title
				.toLowerCase()
				.replace(/\s+/g, "-")
				.replace(/[^\w-]/g, "");
			heading.id = id;
		}

		toc.push({
			title,
			id,
			level,
		});

		// let id =
		// 	heading.getAttribute("id") ||
		// 	title
		// 		.toLowerCase()
		// 		.replace(/\s+/g, "-")
		// 		.replace(/[^a-z0-9-]/g, "");
		// // Ensure ID uniqueness
		// let uniqueId = id;
		// let counter = 1;
		// while (doc.querySelector(`#${uniqueId}`)) {
		// 	uniqueId = `${id}-${counter}`;
		// 	counter++;
		// }
		// heading.setAttribute("id", uniqueId);
		// toc.push({ title, id: uniqueId, level });
	});

	return toc;
};

export default function TocComponent({ html }: { html: string }) {
	const [toc, setToc] = useState<{ title: string; id: string; level: number }[]>([]);
	useEffect(() => {
		const extractedToc = extractTableOfContentsFromHtml(html);
		setToc(extractedToc);
	}, [html]);

	const [activeId, setActiveId] = useState<string | null>(null);
	const observer = useRef<IntersectionObserver | null>(null);
	useEffect(() => {
		if (observer.current) {
			observer.current.disconnect();
		}

		observer.current = new IntersectionObserver(
			(entries) => {
				entries.forEach((entry) => {
					if (entry.isIntersecting) {
						setActiveId(entry.target.id);
					}
				});
			},
			{ rootMargin: "0px 0px -80% 0px", threshold: 0.1 },
		);

		const headingElements = Array.from(document.querySelectorAll("h2, h3, h4, h5, h6"));

		headingElements.forEach((heading) => {
			observer.current?.observe(heading);
		});

		return () => {
			if (observer.current) {
				observer.current.disconnect();
			}
		};
	}, []);

	return (
		<>
			{toc.length > 0 && (
				<div className="hidden shrink-0 md:block md:w-1/4">
					<div className="sticky top-20">
						{/* <div className="rounded-lg bg-gray-50 p-6 shadow-md">
							<h2 className="mb-3 text-lg font-semibold text-blue-900">Table of Contents</h2>
							<nav>
								<ul className="space-y-1">
									{toc.map((item, index) => (
										<li key={index} className={`transition-all duration-200 ease-in-out ${item.level > 1 ? `pl-${(item.level - 2) * 4}` : ""}`}>
											<a href={`#${item.id}`} className="block py-1 text-sm text-gray-700 hover:underline">
												{item.title}
											</a>
										</li>
									))}
								</ul>
							</nav>
						</div> */}
						<div className="">
							<p className="mb-3 flex flex-row items-center text-sm">
								<AlignLeft className="mr-1 h-4 w-4" />
								Table of Contents
							</p>
							<nav>
								<ul>
									{toc.map((item, index) => (
										<li
											key={index}
											className={`transition-all duration-200 ease-in-out ${item.level > 1 ? `pl-${(item.level - 2) * 4}` : ""}`}
										>
											<a
												href={`#${item.id}`}
												className={cn(
													"hover:text-action/80 block py-1 text-sm",
													activeId === item.id ? "text-action font-medium" : "text-muted-foreground hover:text-action/80",
												)}
											>
												{item.title}
											</a>
										</li>
									))}
								</ul>
							</nav>
						</div>
					</div>
				</div>
			)}
		</>
	);
}

// function extractTableOfContents(markdown: string) {
// 	const lines = markdown.split("\n");
// 	const toc: { title: string; id: string; level: number }[] = [];
// 	lines.forEach((line) => {
// 		const match = line.match(/^(#+)\s*(.*)/);
// 		if (match) {
// 			const level = match[1].length;
// 			const title = match[2];
// 			toc.push({
// 				title,
// 				id: title.toLowerCase().replace(/\s+/g, "-"),
// 				level,
// 			});
// 		}
// 	});
// 	return toc;
// }
