import type { Metadata } from "next";
import { OSS_URL_HOST, WEBNAME } from "@/lib/constants";
import FAQsComponent from "@/components/landing/faqs";
import FinalCTA from "@/components/landing/final-cta";
import EditHomeClient from "@/components/app/edit-home.client";
import { getCurrentSessionUser } from "@/server/auth/auth-session";
import { redirect } from "next/navigation";
import FeaturesComponent from "@/components/landing/features";
import RemovePeopleClient from "./remove-people.client";

export const metadata: Metadata = {
	title: `Remove People from Photos with AI | ${WEBNAME}`,
	description: "Easily remove people from photos using AI. Upload your image, describe what you want to remove, and get professional results instantly.",
	alternates: {
		canonical: "/tools/remove-people-from-photos",
	},
};

export default async function Page() {
	const sessionUser = await getCurrentSessionUser();
	if (process.env.NODE_ENV === "production" && sessionUser) redirect("/edit");

	return (
		<main className="min-h-screen pb-24">
			<div className="mb-18 px-4 pb-6 md:px-6">
				<div className="container flex h-full flex-col rounded-2xl px-0 md:rounded-4xl">
					<div className="flex w-full grow flex-col items-center pt-16 md:grow-0">
						<div className="mx-auto flex w-full flex-col items-center gap-y-4 text-center">
							<h1 className="text-[32px] leading-9 font-medium md:text-[40px] md:leading-16 md:font-semibold">
								Remove People from Photos with AI
							</h1>
							<div className="text-muted-foreground text-base leading-none">Easily remove people from photos using AI.</div>
						</div>
						<div className="mt-10 w-full max-w-5xl md:mt-14">
							<RemovePeopleClient />
						</div>
					</div>
				</div>
			</div>
		</main>
	);
}
