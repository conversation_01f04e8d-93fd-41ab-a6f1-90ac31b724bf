"use client";

import { useState } from "react";
import { toast } from "sonner";
import { useSignInBoxOpenStore } from "@/store/useSignInBoxOpenStore";
import { ofetch } from "ofetch";
import { AuthError, IgnoreError, Credits402Error, handleError } from "@/@types/error";
import { SubmitButton } from "@/components/ui/custom/submit-button";
import { Download, LoaderCircle, CoinsIcon, SparklesIcon, PlusIcon, CrownIcon, X } from "lucide-react";
import { Textarea } from "@/components/ui/textarea";
import { cn } from "@/lib/utils";
import { useSession } from "@/lib/auth-client";
import { useUserStore } from "@/store/useUserStore";
import { downloadImageFromBase64, downloadImageFromBase64WithWatermark, imageUrlToBase64 } from "@/lib/file/utils-file";
import { usePlanBoxOpenStore } from "@/store/usePlanBoxOpenStore";
import { IMAGE_SIZE_LIMIT, IMAGE_SIZE_LIMIT_EXCEED_MESSAGE, OSS_URL_HOST } from "@/lib/constants";
import { useDropzone } from "react-dropzone";
import { buttonVariants } from "@/components/ui/button";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { uploadFile } from "@/lib/file/upload-file";
import { EVENT_EDIT_IMAGE, trackGTMEvent } from "@/lib/track-events";
import { WarningBanner } from "@/components/shared/banner-nsfw";
import { useRouter } from "nextjs-toploader/app";

export default function ImageEditor() {
	const router = useRouter();
	const { data: session } = useSession();
	const { user, refreshUser, hasPaid: userHasPaid } = useUserStore();
	const { setSignInBoxOpen } = useSignInBoxOpenStore();
	const { setPlanBoxOpen } = usePlanBoxOpenStore();

	const [showInitialView, setShowInitialView] = useState(true);
	const [prompt, setPrompt] = useState<string>("");
	const [refenceImages, setReferenceImages] = useState<string[]>([]);
	const [image, setImage] = useState<string | null>(null);

	const [allImages, setAllImages] = useState<string[]>([
		// "https://static.editpal.im/dev/media/202508/202508310198feb50304714aac90917333ceb298.jpeg",
		// "https://static.editpal.im/dev/media/202508/202508310198febc947a7649b4608167074ba9c5.jpeg",
		// "https://static.editpal.im/dev/media/202508/202508310198feae689f71c6abd4806eb30a6a4c.jpeg",
	]);
	const {
		getRootProps,
		getInputProps,
		open: openDropzone,
	} = useDropzone({
		noClick: true,
		multiple: false,
		accept: {
			"image/jpeg": [],
			"image/png": [],
			"image/webp": [],
		},
		maxFiles: 1,
		onError: console.error,
		onDrop: async (acceptedFiles, fileRejections, event) => {
			if (!session) {
				setSignInBoxOpen(true);
				return;
			}
			if (fileRejections.length > 0) {
				const message = fileRejections.at(0)?.errors.at(0)?.message;
				console.error?.(new Error(message));
				return;
			}
			let file = acceptedFiles[0];
			if (!file) return;

			if (file.size > IMAGE_SIZE_LIMIT) {
				toast.warning(IMAGE_SIZE_LIMIT_EXCEED_MESSAGE);
				return;
			}

			try {
				setUploadingImage(true);
				const { file_url } = await uploadFile(file);
				setReferenceImages([...refenceImages, file_url]);
				setShowInitialView(false);
			} catch (error: any) {
				console.error("Failed to upload image:", error.message);
				if (error instanceof AuthError) {
					setSignInBoxOpen(true);
					return;
				}
				toast.error(`Upload image failed: ${error.message}`);
			} finally {
				setUploadingImage(false);
			}
		},
	});
	const [uploadingImage, setUploadingImage] = useState<boolean>(false);

	const [submitting, setSubmitting] = useState(false);
	const handleGenerateImage = async () => {
		if (submitting) return;
		if (!session) {
			setSignInBoxOpen(true);
			return;
		}
		const promtpTrim = prompt.trim();
		if (!promtpTrim) return;

		trackGTMEvent(EVENT_EDIT_IMAGE, {
			membership_level: user?.membershipLevel,
		});

		try {
			setShowInitialView(false);
			setSubmitting(true);
			const { status, message, resultUrls } = await ofetch("/api/v1/image/edit-image", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: {
					prompt: promtpTrim,
					images: refenceImages,
				},
			});
			handleError(status, message);
			refreshUser();

			if (resultUrls && resultUrls.length > 0) {
				setImage(resultUrls[0]);
				setAllImages([resultUrls[0], ...allImages]);
			}

			toast.success("Generate success.");
		} catch (error: any) {
			console.error("Failed to generate image:", error);
			if (error instanceof AuthError) {
				setSignInBoxOpen(true);
				return;
			}
			if (error instanceof IgnoreError) {
				return;
			}
			if (error instanceof Credits402Error) {
				toast.warning("You do not have enough credits.", {
					action: {
						label: userHasPaid ? "My billing" : "Get more",
						onClick: () => {
							if (userHasPaid) {
								router.push("/my-billing");
							} else {
								setPlanBoxOpen(true);
							}
						},
					},
				});
				return;
			}
			toast.error(error.message || "Generate failed.");
		} finally {
			setSubmitting(false);
		}
	};

	const [downloading, setDownloading] = useState<boolean>(false);

	return (
		<div className="relative flex h-full w-full flex-col md:flex-row" {...getRootProps()}>
			{allImages.length > 0 && (
				<div className="z-20 flex justify-center px-4 pt-4 md:absolute md:h-full md:flex-col md:pb-4">
					{/* <div className="">
                        <Button
                            variant="secondary"
                            className="hover:ring-input h-[52px] w-[52px] flex-row overflow-hidden rounded-md bg-white shadow-none hover:bg-white hover:ring-2"
                            onClick={openDropzone}
                        >
                            <PlusIcon />
                            <input {...getInputProps()} />
                        </Button>
                    </div> */}
					<div
						className={cn(
							"flex flex-row gap-1 overflow-scroll md:flex-col",
							"rounded-lg bg-[#f1f1f1] p-2 hover:pb-1 md:min-h-[360px] md:hover:pb-2",
							"[&::-webkit-scrollbar]:h-0 [&::-webkit-scrollbar]:w-0 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-thumb]:bg-neutral-300",
							"hover:[&::-webkit-scrollbar]:h-1 md:hover:[&::-webkit-scrollbar]:h-0 md:hover:[&::-webkit-scrollbar]:w-1",
						)}
					>
						{allImages.map((imageOption, index) => (
							<div
								key={index}
								className={cn("relative aspect-square h-[52px] w-[52px] shrink-0 overflow-hidden rounded-md", image === imageOption && "p-0.5")}
							>
								<div
									className={cn(
										"aspect-square cursor-pointer rounded-sm bg-white bg-cover bg-center bg-no-repeat bg-origin-content",
										image === imageOption && "p-0.5 ring-2 ring-blue-500",
									)}
									onClick={() => {
										setShowInitialView(false);
										setImage(imageOption);
									}}
								>
									<img
										src={imageOption}
										alt=""
										className="aspect-square h-full w-full rounded object-cover"
										onContextMenu={(e) => e.preventDefault()}
										onDragStart={(e) => e.preventDefault()}
									/>
								</div>
							</div>
						))}
					</div>
				</div>
			)}

			<div className="flex flex-1 flex-col items-center justify-between gap-4 p-4">
				<div className="flex min-h-0 w-full flex-1 items-center justify-center">
					{showInitialView ? (
						<div className="bg-muted flex w-full max-w-xl flex-col items-center justify-center gap-8 rounded-2xl px-6 pt-14 pb-6 text-center sm:pt-18 sm:pb-12 md:rounded-3xl">
							<div>
								<p className="text-xl font-medium md:text-2xl">Your AI Image Editing Pal</p>
								<p className="pt-4 text-sm font-medium text-neutral-500/80">Upload an image to get started</p>
								{/* <p className="text-muted-foreground text-sm">or select from our library</p> */}
							</div>
							<SubmitButton
								isSubmitting={uploadingImage}
								className="relative mx-auto h-auto overflow-hidden rounded-2xl bg-blue-500 px-6 py-5 hover:bg-blue-500"
								onClick={() => {
									if (session) {
										openDropzone();
									} else {
										setSignInBoxOpen(true);
										return;
									}
								}}
							>
								<div className="flex flex-row items-center gap-2">
									<p className="flex h-4 w-4 items-center justify-center rounded-full bg-white">
										<PlusIcon className="size-3 text-blue-500" strokeWidth={3} />
									</p>
									Upload Image
								</div>
							</SubmitButton>
						</div>
					) : (
						image && (
							<div className="relative flex max-h-[calc(100vh-326px)] flex-col justify-center overflow-hidden md:max-h-[calc(100vh-242px)] md:max-w-[calc(100vw-160px)]">
								{/* <div
								className="aspect-square max-w-full bg-contain bg-center bg-no-repeat"
								// style={{ backgroundImage: `url(${OSS_URL_HOST}mkt/pages/ai-image-generator/demo.webp)` }}
								style={{ backgroundImage: `url(${image})` }}
								role="img"
							></div> */}
								<div className="h-auto w-auto">
									<div className="group relative">
										<img
											src={image}
											alt=""
											className="max-h-[calc(100vh-360px)] max-w-full object-contain md:max-h-[calc(100vh-300px)]"
											onContextMenu={(e) => e.preventDefault()}
											onDragStart={(e) => e.preventDefault()}
										/>

										<div
											className={cn(
												"absolute top-0 right-0 bottom-0 left-0 z-10 items-center gap-1",
												// "md:hidden md:group-hover:flex"
											)}
										>
											<div
												className={cn(
													"relative h-full w-full",
													// "md:bg-gradient-to-t md:from-black/30 md:to-transparent"
												)}
											>
												<div className="absolute right-1 bottom-1">
													<DropdownMenu>
														<DropdownMenuTrigger
															className={cn(
																buttonVariants({ variant: "ghost", size: "icon" }),
																"bg-muted-foreground/50 hover:bg-muted-foreground/60 text-white hover:text-white",
															)}
														>
															{downloading ? <LoaderCircle className="h-4 w-4 animate-spin" /> : <Download />}
														</DropdownMenuTrigger>
														<DropdownMenuContent>
															{!userHasPaid && (
																<DropdownMenuItem
																	className="flex cursor-pointer flex-row items-center gap-2 text-[13px] font-[350] [&>svg]:size-[14px]"
																	onClick={async () => {
																		try {
																			setDownloading(true);
																			const base64 = await imageUrlToBase64(image, { noCache: true });
																			await downloadImageFromBase64WithWatermark(base64);
																		} catch (error) {
																			console.error("Failed to download image:", error);
																		} finally {
																			setDownloading(false);
																		}
																	}}
																>
																	<Download className="text-zinc-800" /> Download with watermark
																</DropdownMenuItem>
															)}
															<DropdownMenuItem
																className="flex cursor-pointer flex-row items-center gap-2 text-[13px] font-[350] [&>svg]:size-[14px]"
																onClick={async () => {
																	if (!userHasPaid) {
																		setPlanBoxOpen(true);
																		return;
																	}
																	try {
																		setDownloading(true);
																		const base64 = await imageUrlToBase64(image, { noCache: true });
																		await downloadImageFromBase64(base64);
																	} catch (error) {
																		console.error("Failed to download image:", error);
																	} finally {
																		setDownloading(false);
																	}
																}}
															>
																{userHasPaid ? (
																	<>
																		<Download className="text-zinc-800" /> Download
																	</>
																) : (
																	<>
																		<CrownIcon className="text-yellow-500" /> Download without watermark
																	</>
																)}
															</DropdownMenuItem>
														</DropdownMenuContent>
													</DropdownMenu>
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
						)
					)}
				</div>

				{/* NSFW Warning */}
				<WarningBanner localStorageKey="nsfw-warning-dismissed-image-editing" />

				<div className="bg-muted w-full max-w-xl shrink-0 rounded-xl backdrop-blur-2xl md:rounded-2xl lg:max-w-2xl">
					{refenceImages.length > 0 && (
						<div className="flex flex-row flex-wrap items-center gap-1 px-3 py-1.5">
							{refenceImages.map((refenceImage, index) => (
								<div key={index} className="group relative h-14 w-14">
									<img
										src={refenceImage}
										alt="Model"
										className="h-full w-full rounded-md object-cover"
										onContextMenu={(e) => e.preventDefault()}
										onDragStart={(e) => e.preventDefault()}
									/>
									<button
										className="absolute -top-1.5 -right-1.5 z-10 rounded-full"
										onClick={(e) => {
											e.stopPropagation();
											setReferenceImages(refenceImages.filter((_, i) => i !== index));
										}}
									>
										<X className="size-[18px] rounded-full bg-zinc-900 p-1 text-white" strokeWidth={3} />
									</button>
								</div>
							))}
						</div>
					)}

					<Textarea
						placeholder="What do you want to change?"
						maxLength={2000}
						className="max-h-[64px] min-h-[56px] resize-none border-none text-sm shadow-none focus-visible:ring-0 [&::-webkit-scrollbar]:hidden"
						value={prompt}
						onChange={(e) => setPrompt(e.target.value)}
					/>

					<div className="flex flex-row flex-wrap items-center justify-between gap-1 px-3 pt-1 pb-2">
						<SubmitButton
							variant="secondary"
							size="sm"
							className="justify-between rounded-full bg-white text-[12px] font-normal shadow-none hover:bg-white"
							isSubmitting={uploadingImage}
							onClick={() => {
								if (session) {
									openDropzone();
								} else {
									setSignInBoxOpen(true);
									return;
								}
							}}
							{...{ disabled: uploadingImage || refenceImages.length >= 4 }}
						>
							Add image{refenceImages.length > 0 && `(${refenceImages.length}/4)`}
						</SubmitButton>
						{/* <Button variant="default" size="icon" className="size-8">
							<ArrowUpIcon className="size-4" />
						</Button> */}
						<SubmitButton
							variant="default"
							size="lg"
							// size="icon"
							// className="size-8"
							className="font-normal"
							isSubmitting={submitting}
							onClick={handleGenerateImage}
							{...{ disabled: submitting || !prompt.trim() || !refenceImages.length }}
						>
							<SparklesIcon className="size-4" />
							Generate
						</SubmitButton>
					</div>
					{/* <div className="flex flex-row items-center justify-between gap-1 px-1">
						<SubmitButton
							className="bg-foreground hover:bg-foreground"
							isSubmitting={submitting}
							onClick={handleGenerateImage}
							{...{ disabled: submitting || !prompt.trim() || !image }}
						>
							<SparklesIcon />
							Generate
						</SubmitButton>
					</div> */}
				</div>
			</div>
			<input {...getInputProps()} />
		</div>
	);
}
