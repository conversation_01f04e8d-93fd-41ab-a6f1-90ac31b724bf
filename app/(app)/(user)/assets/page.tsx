import { WEBNAME } from "@/lib/constants";
import { getCurrentSessionUser } from "@/server/auth/auth-session";
import SignIn from "@/components/shared/sigin-in";
import type { Metadata } from "next";
import MyAssets from "./my-assets.client";

export const metadata: Metadata = {
	title: `My Assets | ${WEBNAME}`,
	alternates: {
		canonical: "/assets",
	},
	robots: {
		index: false,
		follow: false,
	},
};

export default async function Page() {
	// 1. Check user
	const sessionUser = await getCurrentSessionUser();
	// let user: User | null = null;
	// if (sessionUser) {
	// 	user = await getUser(sessionUser.id);
	// }
	if (!sessionUser) {
		return (
			<div className="flex h-full min-h-screen items-center justify-center">
				<div className="bg-muted w-full max-w-md rounded-xl px-6 py-8">
					<SignIn />
				</div>
			</div>
		);
	}

	// 2. Check user has paid
	if (process.env.NODE_ENV === "development") {
		return <MyAssets isHasPaid={true} />;
	}
	// const isHasPaid = userHasPaid(user.membershipId, user.creditOneTimeEndsAt);
	// if (isHasPaid) {
	// 	return <MyCreation isHasPaid={true} />;
	// }

	return <MyAssets isHasPaid={false} />;
}
