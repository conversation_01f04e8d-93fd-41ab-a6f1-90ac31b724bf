import { WEBNAME } from "@/lib/constants";
import type { Metadata } from "next";
import { ConfirmationClient } from "./confirmation.client";

export const metadata: Metadata = {
	title: `Confirmation | ${WEBNAME}`,
	alternates: {
		canonical: "/user/confirmation",
	},
	robots: {
		index: false,
		follow: false,
	},
};
export default function Page() {
	return (
		<div className="flex min-h-screen flex-col items-center justify-center p-6">
			<ConfirmationClient />
		</div>
	);
}
