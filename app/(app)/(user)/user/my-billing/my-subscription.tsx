"use client";

import { useEffect, useState } from "react";
import { <PERSON><PERSON>, buttonVariants } from "@/components/ui/button";
import { toast } from "sonner";
import { UserInfoDB } from "@/@types/user";
import { Card, CardHeader } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { MembershipID, membershipMapping } from "@/@types/membership-type";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useRouter } from "nextjs-toploader/app";
import { Dialog, DialogContent, DialogTitle } from "@/components/ui/dialog";
import { Subscription } from "@/@types/subscription";
import { AuthError, handleError } from "@/@types/error";
import { cn } from "@/lib/utils";
import { ofetch } from "ofetch";
import { usePlanBoxOpenStore } from "@/store/usePlanBoxOpenStore";
import { useSignInBoxOpenStore } from "@/store/useSignInBoxOpenStore";
import { getMembershipProductInfo, getSubscriptionStatusName, SubscriptionStatus } from "@/lib/utils-membership";
import { ArrowUpRight, EllipsisVertical, Loader2 } from "lucide-react";
import { format } from "date-fns";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import Plans from "@/components/shared/plans-subscription";
import { EMAIL_CONTACT } from "@/lib/constants";
import { SubmitButton } from "@/components/ui/custom/submit-button";
import { EVENT_OPEN_PLAN_ID } from "@/lib/track-events";
import { useUserStore } from "@/store/useUserStore";

export default function MySubscription({ user, subscriptions, hasOrder }: { user: UserInfoDB; subscriptions: Subscription[]; hasOrder: boolean }) {
	const router = useRouter();
	const { setSignInBoxOpen } = useSignInBoxOpenStore();
	const { setUser, creditsAll: userCreditsAll, hasPaid: userHasPaid } = useUserStore();
	const { setPlanBoxOpen } = usePlanBoxOpenStore();

	useEffect(() => {
		setUser(user);
	}, [user]);

	const [changePlanBoxOpen, setChangePlanBoxOpen] = useState(false);
	const [changePlanSubscription, setChangePlanSubscription] = useState<Subscription | null>(null);
	const [isResuming, setIsResuming] = useState(false);
	const [handleSubscription, setHandleSubscription] = useState<{
		type: "resume" | "cancel" | "portal" | string;
		subscriptionId?: string;
	} | null>(null);
	const [isGettingCustomerPortalUrl, setIsGettingCustomerPortalUrl] = useState(false);

	// ** Suspend usage due to API error
	const resumeSubscription = async (subscriptionId: string) => {
		if (isResuming) return;
		try {
			setIsResuming(true);
			const { status, message } = await ofetch("/api/payment/subscription", {
				method: "POST",
				body: { subscriptionId, type: "resume" },
			});
			handleError(status, message);
			router.refresh();
			toast.success("Subscription resumed.");
		} catch (error) {
			console.error(error);
			if (error instanceof AuthError) {
				setSignInBoxOpen(true);
				return;
			}
			toast.error("Subscription resume failed.");
		} finally {
			setIsResuming(false);
		}
	};

	const getCustomerPortalUrl = async (type: string, subscriptionId?: string) => {
		if (isGettingCustomerPortalUrl) return;
		try {
			setHandleSubscription({ type, subscriptionId });
			setIsGettingCustomerPortalUrl(true);
			const { status, message, url } = await ofetch("/api/payment/portal", {
				method: "POST",
				body: {},
			});
			handleError(status, message);
			window.open(url, "_blank");
		} catch (error: any) {
			console.error(error);
			if (error instanceof AuthError) {
				setSignInBoxOpen(true);
				return;
			}
			if (error.message?.includes("Customer does not exist")) {
				toast.error("Customer does not exist");
				return;
			}
			toast.error("Failed to open customer portal.");
		} finally {
			setHandleSubscription(null);
			setIsGettingCustomerPortalUrl(false);
		}
	};

	return (
		<div className="flex h-full w-full flex-grow flex-col overflow-hidden">
			<ScrollArea className="h-full w-full px-6 md:px-8">
				<div className="mx-auto flex w-full max-w-3xl pt-20 pb-2">
					<h1 className="text-2xl font-medium whitespace-nowrap">My subscription</h1>
				</div>

				<div className="mx-auto flex max-w-3xl flex-col gap-6 pt-4">
					<div className="flex flex-col gap-4">
						<p className="text-muted-foreground leading-none font-medium tracking-tight">Plan</p>
						<div className="flex flex-col gap-3">
							{user.membershipId === MembershipID.Free && (
								<Card className="relative w-full rounded-md p-0 shadow-none">
									{subscriptions.length > 0 && (
										<Badge className="absolute right-0 -translate-y-1/2 rounded-full bg-yellow-500 font-normal shadow-none hover:bg-yellow-500">
											Current plan
										</Badge>
									)}
									<CardHeader className="min-h-[56px] grid-rows-none px-4 py-4">
										<div className="flex flex-row items-center justify-between gap-2">
											<div className="flex w-full flex-row items-center gap-1">
												<p className="font-medium">Free</p>
											</div>

											<Button
												id={EVENT_OPEN_PLAN_ID}
												size="sm"
												onClick={() => setPlanBoxOpen(true)}
												className="rounded-full bg-blue-500 hover:bg-blue-500/80"
											>
												Upgrade
											</Button>
										</div>
									</CardHeader>
								</Card>
							)}
							{user.membershipId !== MembershipID.Free && subscriptions.length === 0 && (
								<Card className="w-full rounded-md bg-red-500/30 p-0 shadow-none">
									<CardHeader className="flex h-[56px] flex-row items-center justify-between gap-2 px-4 py-2.5">
										<div className="flex w-full flex-row items-center gap-1">
											<p className="leading-none font-medium">{membershipMapping[user.membershipId as MembershipID].name}</p>
										</div>
									</CardHeader>
								</Card>
							)}
							{subscriptions.map((subscription, index) => {
								const currentSubMembershipProductInfo = getMembershipProductInfo(subscription.productId);
								return (
									<Card key={index} className="relative w-full rounded-md p-0 shadow-none">
										{subscriptions.length > 1 && subscription.subscriptionId === user.subscriptionId && (
											<Badge
												variant="default"
												className="absolute right-0 -translate-y-1/2 rounded-full bg-yellow-600 font-normal shadow-none hover:bg-yellow-600"
											>
												Current plan
											</Badge>
										)}
										<CardHeader className="space-y-0 px-4 py-4">
											<div className="flex flex-row items-center justify-between gap-2">
												<div className="flex flex-row items-center gap-1">
													<p className="leading-none font-medium">
														{currentSubMembershipProductInfo?.membership.name}
														<span className="text-muted-foreground ml-1 text-sm">
															({currentSubMembershipProductInfo?.period.name})
														</span>
													</p>
													<Badge
														variant="secondary"
														className={cn(
															subscription.status === SubscriptionStatus.Active
																? "bg-green-100 text-green-600 hover:bg-green-100 hover:text-green-600"
																: "bg-yellow-50 text-yellow-600 hover:bg-yellow-50 hover:text-yellow-600",
															"rounded-full font-normal shadow-none",
														)}
													>
														{getSubscriptionStatusName(subscription.status as SubscriptionStatus)}
													</Badge>
												</div>
											</div>
											<div className="text-muted-foreground flex flex-row items-center gap-1 text-xs">
												{subscription.cancelAtPeriodEnd ? (
													<>
														{subscription.currentPeriodEndAt && (
															<p className="">Ends on {format(subscription.currentPeriodEndAt, "MMM d, yyyy")}</p>
														)}
														<span> · </span>
														<button
															className="border-foreground text-brand-success border-b-brand-success hover:text-foreground flex flex-row items-center border-b"
															onClick={() => {
																getCustomerPortalUrl("resume", subscription.subscriptionId);
																// resumeSubscription(subscription.subscriptionId);
															}}
														>
															Resume
															{handleSubscription?.type === "resume" &&
																handleSubscription.subscriptionId === subscription.subscriptionId && (
																	<Loader2 className="h-4 w-4 animate-spin" />
																)}
														</button>
													</>
												) : (
													<>
														{subscription.currentPeriodEndAt && (
															<p className="">Next renews on {format(subscription.currentPeriodEndAt, "MMM d, yyyy")}</p>
														)}
													</>
												)}
											</div>
											<div className="mt-1 flex flex-row items-center gap-1">
												<Button
													size="sm"
													variant={currentSubMembershipProductInfo?.membership.id === MembershipID.Premium ? "secondary" : "default"}
													onClick={() => {
														setChangePlanSubscription(subscription);
														setChangePlanBoxOpen(true);
													}}
													className="font- h-7 text-xs"
												>
													{currentSubMembershipProductInfo?.membership.id === MembershipID.Premium ? "Change plan" : "Upgrade plan"}
												</Button>

												{!subscription.cancelAtPeriodEnd && (
													<Button
														size="sm"
														variant="ghost"
														onClick={() => {
															if (isGettingCustomerPortalUrl) return;
															getCustomerPortalUrl("cancel", subscription.subscriptionId);
														}}
														disabled={
															isGettingCustomerPortalUrl &&
															handleSubscription?.type === "cancel" &&
															handleSubscription.subscriptionId === subscription.subscriptionId
														}
														className="text-secondary-foreground h-7 gap-0.5 text-xs font-normal"
													>
														Manage subscription
														{isGettingCustomerPortalUrl &&
														handleSubscription?.type === "cancel" &&
														handleSubscription.subscriptionId === subscription.subscriptionId ? (
															<Loader2 className="size-3 animate-spin" />
														) : (
															<ArrowUpRight className="size-3" />
														)}
													</Button>
												)}
											</div>
										</CardHeader>
									</Card>
								);
							})}

							{((user.membershipId === MembershipID.Free && subscriptions.length > 0) || subscriptions.length > 1) && (
								<div className="rounded-md bg-yellow-100 p-4">
									<p className="text-sm text-yellow-600">
										We{"'"}ve detected an issue with your subscription information. Please{" "}
										<a href={`mailto:${EMAIL_CONTACT}`} className="text-foreground font-medium underline">
											contact our support team
										</a>{" "}
										for assistance in resolving this matter.
									</p>
								</div>
							)}
						</div>
					</div>

					<div className="flex flex-col gap-4">
						<p className="text-muted-foreground leading-none font-medium tracking-tight">Credits</p>

						<Card className="w-full rounded-md p-0 shadow-none">
							<CardHeader className="grid-rows-none px-4 py-4">
								<div className="flex flex-col gap-1">
									<div className="flex flex-row items-center justify-between gap-2">
										<p className="leading-none font-medium">{userCreditsAll}</p>
									</div>
									<div className="">
										<p className="text-muted-foreground text-xs">5 credits = 1 image generation</p>
									</div>
								</div>
							</CardHeader>
						</Card>
					</div>

					{hasOrder && (
						<div className="flex flex-col gap-4">
							<p className="text-muted-foreground leading-none font-medium tracking-tight">Billing information</p>

							<Card className="w-full rounded-md p-0 shadow-none">
								<CardHeader className="grid-rows-none px-4 py-4">
									<div className="flex flex-row items-center justify-between gap-2">
										<div className="flex flex-row items-center gap-1">
											<p className="text-sm">{user.email}</p>
										</div>

										<SubmitButton
											isSubmitting={isGettingCustomerPortalUrl && handleSubscription?.type === "portal"}
											variant="ghost"
											onClick={() => getCustomerPortalUrl("portal")}
											className="cursor-pointer rounded-full text-blue-500 hover:text-blue-500"
										>
											Billing history
										</SubmitButton>
									</div>
								</CardHeader>
							</Card>
						</div>
					)}

					<Dialog open={changePlanBoxOpen} onOpenChange={setChangePlanBoxOpen}>
						<DialogTitle className="h-0" />
						<DialogContent className="rounded-lg p-0 sm:max-w-4xl">
							<ScrollArea className="max-h-[90vh] overflow-y-auto" type="always">
								<div className="flex flex-col items-center gap-2 p-6 md:p-8">
									<Plans hasFree={false} userSubscription={changePlanSubscription} />
								</div>
							</ScrollArea>
						</DialogContent>
					</Dialog>
				</div>
			</ScrollArea>
		</div>
	);
}
