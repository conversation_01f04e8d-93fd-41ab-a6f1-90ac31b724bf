import { WEBNAME } from "@/lib/constants";
import { UserInfoDB } from "@/@types/user";
import { getCurrentSessionUser } from "@/server/auth/auth-session";
import { refreshUser } from "@/server/refresh-user";
import { eq, and, desc } from "drizzle-orm";
import { getDB } from "@/server/db/db-client.server";
import SignIn from "@/components/shared/sigin-in";
import type { Metadata } from "next";
import { orderSchema, subscriptionSchema, User, userSchema } from "@/server/db/schema.server";
import MySubscription from "./my-subscription";

type Params = Promise<{ lang: string }>;

export const metadata: Metadata = {
	title: `My Billing | ${WEBNAME}`,
	alternates: {
		canonical: "/user/my-billing",
	},
	robots: {
		index: false,
		follow: false,
	},
};

async function getMySubscriptions() {
	const sessionUser = await getCurrentSessionUser();
	if (!sessionUser) return { userInfo: null, subscriptions: [] };
	const userId = sessionUser.id;

	const db = getDB();
	const result = await db.transaction(async (tx) => {
		const userQuery = tx.select().from(userSchema).where(eq(userSchema.id, userId)).limit(1);
		const subscriptionsQuery = tx
			.select({
				id: subscriptionSchema.id,
				userId: subscriptionSchema.userId,

				subscriptionId: subscriptionSchema.subscriptionId,
				status: subscriptionSchema.status,
				recurringInterval: subscriptionSchema.recurringInterval,

				productId: subscriptionSchema.productId,

				currentPeriodStartAt: subscriptionSchema.currentPeriodStartAt,
				currentPeriodEndAt: subscriptionSchema.currentPeriodEndAt,
				cancelAtPeriodEnd: subscriptionSchema.cancelAtPeriodEnd,
				canceledAt: subscriptionSchema.canceledAt,
				startAt: subscriptionSchema.startAt,
				endAt: subscriptionSchema.endAt,
				endedAt: subscriptionSchema.endedAt,
			})
			.from(subscriptionSchema)
			// .where(and(eq(subscriptionSchema.userId, userInfo.id), or(eq(subscriptionSchema.status, "cancelled"), eq(subscriptionSchema.status, "active"))))
			.where(and(eq(subscriptionSchema.userId, userId), eq(subscriptionSchema.status, "active")));
		const latestOrderQuery = tx
			.select({ id: orderSchema.id })
			.from(orderSchema)
			.where(eq(orderSchema.userId, userId))
			.orderBy(desc(orderSchema.id))
			.limit(1);

		// 使用 Promise.all 并行执行所有查询，提高效率
		const [userResult, subscriptionsResult, latestOrderResult] = await Promise.all([userQuery, subscriptionsQuery, latestOrderQuery]);

		const user = userResult[0]; // .limit(1) 返回数组，取第一个元素

		if (!user) {
			return null;
		}
		return {
			user: user,
			subscriptions: subscriptionsResult,
			latestOrder: latestOrderResult[0] || null,
		};
	});
	if (!result) return { userInfo: null, subscriptions: [], hasOrder: false };

	const userInfo: UserInfoDB | null = await refreshUser(userId, {
		existUser: result.user,
	});

	return { userInfo, subscriptions: result.subscriptions, hasOrder: !!result.latestOrder };
}

async function getUser() {
	const sessionUser = await getCurrentSessionUser();
	if (!sessionUser) return { userInfo: null, hasOrder: false };

	const db = getDB();
	const { user, order } = await db.transaction(async (tx) => {
		const [user]: User[] = await tx.select().from(userSchema).where(eq(userSchema.id, sessionUser.id));
		const [order] = await tx.select({ id: orderSchema.id }).from(orderSchema).where(eq(orderSchema.userId, sessionUser.id)).limit(1);
		return { user, order };
	});
	if (!user) {
		return { userInfo: null, hasOrder: false };
	}

	const userInfo: UserInfoDB | null = await refreshUser(sessionUser.id, {
		existUser: user,
	});
	if (!userInfo) return { userInfo: null, hasOrder: false };

	return { userInfo, hasOrder: order ? true : false };
}

export default async function Page({ params }: { params: Params }) {
	// const { lang } = await params;

	// const { userInfo: userInfo, hasOrder } = await getUser();
	const { userInfo: userInfo, hasOrder, subscriptions } = await getMySubscriptions();

	if (!userInfo) {
		return (
			<div className="flex h-full min-h-screen items-center justify-center">
				<div className="bg-muted -mt-14 w-full max-w-md rounded-xl px-6 py-8">
					<SignIn />
				</div>
			</div>
		);
	}

	return (
		<div className="min-h-screen">
			<MySubscription user={userInfo} subscriptions={subscriptions} hasOrder={hasOrder} />
			{/* <MyBilling user={userInfo} hasOrder={hasOrder} /> */}
		</div>
	);
}
