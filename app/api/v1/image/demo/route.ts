import { NextResponse } from "next/server";
import { WEBNAME } from "@/lib/constants";
import { handleApiError } from "@/@types/error-api";
import { falClient } from "@/server/ai/fal-config.server";
import { ValidationError } from "@fal-ai/client";
import { UnprocessableError } from "@/@types/error";

type Params = {
	prompt: string;
	images: string[];
};

export async function POST(req: Request) {
	return NextResponse.json({ message: "not found" }, { status: 404 });
	// try {
	// 	const result = await falClient.queue.result("fal-ai/nano-banana/edit", {
	// 		requestId: "10d785f8-3e6b-4a00-812d-41bea4fe7e7e",
	// 	});
	// 	console.log("result: ", JSON.stringify(result, null, 2));

	// 	return NextResponse.json({ message: "Success" });
	// } catch (error: any) {
	// 	if (error instanceof ValidationError) {
	// 		console.log("error: ", JSON.stringify(error, null, 2));
	// 		const errorDetail = error.body.detail[0];
	// 		const errorStatus = error.status;
	// 		const errorType = errorDetail.type;
	// 		console.log("errorType: ", errorType);
	// 		if (errorStatus === 422 && errorType === "content_policy_violation") {
	// 			return handleApiError(new UnprocessableError(errorDetail.msg), `${WEBNAME} - /api/v1/image/demo`);
	// 		}
	// 		if (errorStatus === 422 && errorType === "value_error") {
	// 			return handleApiError(
	// 				new UnprocessableError("Cannot generate an image with your input. Please try again using different input."),
	// 				`${WEBNAME} - /api/v1/image/demo`,
	// 			);
	// 		}
	// 	}
	// 	return handleApiError(error, `${WEBNAME} - /api/v1/image/demo`);
	// }
}
