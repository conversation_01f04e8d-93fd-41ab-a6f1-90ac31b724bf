import { NextResponse } from "next/server";
import { getDB } from "@/server/db/db-client.server";
import { notifyDevEvent } from "@/server/dev-notify.server";
import { WEBNAME } from "@/lib/constants";
import { eq } from "drizzle-orm";
import { MediaResultStatus } from "@/@types/media/media-type";
import { handleApiError } from "@/@types/error-api";
import crypto from "crypto";

// Wavespeed webhook payload structure based on documentation
type WavespeedWebhookPayload = {
	id: string;
	model: string;
	input: {
		prompt: string;
		text: string;
	};
	outputs?: string[]; // Only present when status is "completed"
	status: "completed" | "failed";
	created_at: string;
	error?: string; // Contains error details when status is "failed"
};

// Verify Wavespeed webhook signature according to their documentation
function verifyWaveSpeedAISignature(payload: string, headers: Headers, secret: string): boolean {
	const id = headers.get("webhook-id");
	const timestamp = headers.get("webhook-timestamp");
	const signatureHeader = headers.get("webhook-signature");

	if (!id || !timestamp || !signatureHeader) {
		throw new Error("Missing required headers");
	}

	const [version, receivedSignature] = signatureHeader.split(",");
	if (version !== "v3") {
		throw new Error("Invalid signature version");
	}

	const signedContent = `${id}.${timestamp}.${payload}`;
	const expectedSignature = crypto.createHmac("sha256", secret).update(signedContent).digest("hex");

	// Validate timestamp (reject requests older than 1 hour)
	const ageSeconds = Math.abs(Date.now() / 1000 - parseInt(timestamp, 10));
	if (ageSeconds > 3600) {
		throw new Error("Signature timestamp too old");
	}

	// Use constant-time comparison to prevent timing attacks
	if (!crypto.timingSafeEqual(Buffer.from(expectedSignature), Buffer.from(receivedSignature))) {
		throw new Error("Invalid signature");
	}

	return true;
}

export async function POST(req: Request): Promise<NextResponse> {
	try {
		// Get webhook secret from environment variables
		const webhookSecret = process.env.WAVESPEED_WEBHOOK_SECRET;
		if (!webhookSecret) {
			console.error("WAVESPEED_WEBHOOK_SECRET is not configured");
			return NextResponse.json({ error: "Webhook secret not configured" }, { status: 500 });
		}

		// Get raw body for signature verification
		const rawBody = await req.text();

		// Verify webhook signature
		try {
			verifyWaveSpeedAISignature(rawBody, req.headers, webhookSecret);
		} catch (error: any) {
			console.error("Webhook signature verification failed:", error.message);
			return NextResponse.json({ error: "Invalid webhook signature" }, { status: 401 });
		}

		// Parse the webhook payload
		const body: WavespeedWebhookPayload = JSON.parse(rawBody);

		if (process.env.NODE_ENV === "development") {
			console.log("Wavespeed webhook payload: ", JSON.stringify(body, null, 2));
		}

		const requestId = body.id;
		const db = getDB();

		// Find the voice model task by thirdRequestId
		// const [voiceModelTask]: VoiceModelTask[] = await db
		// 	.select()
		// 	.from(voiceModelTaskSchema)
		// 	.where(eq(voiceModelTaskSchema.thirdRequestId, requestId))
		// 	.limit(1);

		// if (!voiceModelTask) {
		// 	console.error("Voice model task not found for request ID:", requestId);
		// 	return NextResponse.json({ error: "Voice model task not found" }, { status: 404 });
		// }

		// if (voiceModelTask.status === MediaResultStatus.Completed || voiceModelTask.status === MediaResultStatus.Failed) {
		// 	console.log("Voice model task already processed:", requestId);
		// 	return NextResponse.json({ message: "Task already processed" }, { status: 200 });
		// }

		// Handle the webhook payload based on the status
		if (body.status === "completed") {
			if (!body.outputs || body.outputs.length === 0) {
				console.error("No outputs found in completed task:", requestId);
				// await db
				// 	.update(voiceModelTaskSchema)
				// 	.set({
				// 		status: MediaResultStatus.Failed,
				// 		error: "No outputs found in completed task",
				// 	})
				// 	.where(eq(voiceModelTaskSchema.thirdRequestId, requestId));
				return NextResponse.json({ error: "No outputs found" }, { status: 400 });
			}

			const resultUrl = body.outputs[0];

			if (process.env.NODE_ENV === "development") {
				console.log("Voice result URL: ", resultUrl);
			}

			// Save the audio file to R2
			// const mediaPath = await saveVoiceModelPreviewToR2(resultUrl, "custom");

			// await db.transaction(async (tx) => {
			// 	await tx.insert(voiceModelSchema).values({
			// 	});
			// 	await tx
			// 		.update(voiceModelTaskSchema)
			// 		.set({
			// 			status: MediaResultStatus.Completed,
			// 		})
			// 		.where(eq(voiceModelTaskSchema.thirdRequestId, requestId));
			// });

			if (process.env.NODE_ENV === "development") {
				console.log("Voice model created successfully for request ID:", requestId);
			}
		} else if (body.status === "failed") {
			const errorMessage = body.error || "Unknown error occurred";

			// Update the voice model task status to failed
			// await db
			// 	.update(voiceModelTaskSchema)
			// 	.set({
			// 		error: errorMessage,
			// 	})
			// 	.where(eq(voiceModelTaskSchema.thirdRequestId, requestId));

			console.error("Voice model task failed:", requestId, errorMessage);

			// Notify developers about the failure
			notifyDevEvent(`${WEBNAME} - wavespeed voice-design webhook error`, `request id: ${requestId}`, `${errorMessage}`, null);
		}

		return NextResponse.json({ message: "Webhook processed successfully" }, { status: 200 });
	} catch (error: any) {
		console.error("Error processing Wavespeed voice-design webhook:", error);
		return handleApiError(error, `${WEBNAME} - /api/webhook/wavespeed/voice-design`);
	}
}
