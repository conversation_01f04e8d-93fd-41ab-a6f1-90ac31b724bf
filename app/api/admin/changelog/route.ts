import { NextResponse } from "next/server";
import { getDB } from "@/server/db/db-client.server";
import { checkAuthAdmin } from "@/server/auth/check-auth-admin";
import { ChangelogWithPartialTranslations } from "@/@types/admin/changelog/changelog";
import { changelogSchema, changelogTranslationSchema } from "@/server/db/schema.server";
import { desc, eq } from "drizzle-orm";
import { z } from "zod";
import { getKVKeyChangelogHeads } from "@/lib/utils";
import { deleteValue } from "@/server/kv/redis-upstash.server";

export async function GET(req: Request) {
	if (!checkAuthAdmin()) {
		return NextResponse.json({ status: 401, message: "Not authorized." });
	}

	// 从URL查询参数中获取分页信息
	const { searchParams } = new URL(req.url);
	const page = parseInt(searchParams.get("page") || "1");
	const pageSize = parseInt(searchParams.get("pageSize") || "10");

	// 验证分页参数
	if (page < 1 || pageSize < 1 || pageSize > 100) {
		return NextResponse.json({ status: 400, message: "Invalid pagination parameters." });
	}

	const offset = (page - 1) * pageSize;

	if (process.env.NODE_ENV === "development") {
		console.log("pagination params:", { page, pageSize, offset });
	}

	const db = getDB();

	// 获取总数
	const totalCount = await db.$count(changelogSchema);

	// 获取分页数据
	const changelogs: ChangelogWithPartialTranslations[] = await db.query.changelogSchema.findMany({
		with: {
			translations: {
				columns: {
					id: true,
					changelogId: true,
					lang: true,
					status: true,
					title: true,
					publishedAt: true,
					createdAt: true,
					updatedAt: true,
				},
			},
		},
		orderBy: [desc(changelogSchema.publishedAt)],
		limit: pageSize,
		offset: offset,
	});

	return NextResponse.json({
		status: 200,
		changelogs,
		pagination: {
			page,
			pageSize,
			totalCount,
			totalPages: Math.ceil(totalCount / pageSize),
		},
	});
}

// Schema for validating changelog creation/update requests
const changelogRequestSchema = z.object({
	id: z.number().int().optional(),
	version: z.string().trim().min(1, "Version is required"),
	title: z.string().trim().min(1, "Title is required"),
	image: z.string().nullable().optional(),
	publishedAt: z.string().transform((val) => {
		const date = new Date(val);
		if (isNaN(date.getTime())) {
			throw new Error("Invalid date format");
		}
		return date;
	}),
	publishedAtChanged: z.boolean().optional(),
	hasTranslations: z.boolean().optional(),
	languages: z.array(z.string()).optional(),
});

export async function POST(req: Request) {
	if (!checkAuthAdmin()) {
		return NextResponse.json({ status: 401, message: "Not authorized." });
	}

	try {
		const body = await req.json();
		if (process.env.NODE_ENV === "development") {
			console.log("body:", body);
		}
		const validatedData = changelogRequestSchema.parse(body);

		const db = getDB();

		if (validatedData.id) {
			// Update existing changelog
			const updatedChangelog = await db
				.update(changelogSchema)
				.set({
					version: validatedData.version,
					title: validatedData.title,
					image: validatedData.image,
					publishedAt: validatedData.publishedAt,
				})
				.where(eq(changelogSchema.id, validatedData.id))
				.returning({ id: changelogSchema.id });

			if (updatedChangelog.length === 0) {
				return NextResponse.json({ status: 404, message: "Changelog not found." });
			}

			if (validatedData.publishedAtChanged && validatedData.hasTranslations) {
				await db
					.update(changelogTranslationSchema)
					.set({ publishedAt: validatedData.publishedAt })
					.where(eq(changelogTranslationSchema.changelogId, validatedData.id));
			}

			if (validatedData.languages) {
				validatedData.languages.forEach(async (lang) => {
					await deleteValue(getKVKeyChangelogHeads(lang, 1));
				});
			}

			return NextResponse.json({
				status: 200,
				message: "Changelog updated successfully",
				changelogId: updatedChangelog[0].id,
			});
		} else {
			// Create new changelog
			const newChangelog = await db
				.insert(changelogSchema)
				.values({
					version: validatedData.version,
					title: validatedData.title,
					image: validatedData.image,
					publishedAt: validatedData.publishedAt,
				})
				.returning({ id: changelogSchema.id });

			return NextResponse.json({
				status: 200,
				message: "Changelog created successfully",
				changelogId: newChangelog[0].id,
			});
		}
	} catch (error) {
		console.error("Error processing changelog request:", error);

		if (error instanceof z.ZodError) {
			return NextResponse.json({
				status: 400,
				message: "Invalid request data",
				errors: error.errors,
			});
		}

		return NextResponse.json({
			status: 500,
			message: "Internal server error",
		});
	}
}
