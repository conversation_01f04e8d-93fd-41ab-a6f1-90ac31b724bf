import { NextResponse } from "next/server";
import { getDB } from "@/server/db/db-client.server";
import { checkAuthAdmin } from "@/server/auth/check-auth-admin";
import { blogPostSchema } from "@/server/db/schema.server";
import { desc, eq } from "drizzle-orm";
import { z } from "zod";
import { getKVKeyBlogHeads } from "@/lib/utils";
import { deleteValue } from "@/server/kv/redis-upstash.server";
import { BlogPostWithPartialTranslations } from "@/@types/admin/blog/blog";

export async function GET(req: Request) {
	if (!checkAuthAdmin()) {
		return NextResponse.json({ status: 401, message: "Not authorized." });
	}

	// 从URL查询参数中获取分页信息
	const { searchParams } = new URL(req.url);
	const page = parseInt(searchParams.get("page") || "1");
	const pageSize = parseInt(searchParams.get("pageSize") || "10");

	// 验证分页参数
	if (page < 1 || pageSize < 1 || pageSize > 100) {
		return NextResponse.json({ status: 400, message: "Invalid pagination parameters." });
	}

	const offset = (page - 1) * pageSize;

	if (process.env.NODE_ENV === "development") {
		console.log("pagination params:", { page, pageSize, offset });
	}

	const db = getDB();

	// 获取总数
	const totalCount = await db.$count(blogPostSchema);

	// 获取分页数据
	const blogPosts: BlogPostWithPartialTranslations[] = await db.query.blogPostSchema.findMany({
		with: {
			translations: {
				columns: {
					id: true,
					postId: true,
					lang: true,
					status: true,
					title: true,
					publishedAt: true,
					createdAt: true,
					updatedAt: true,
				},
			},
		},
		orderBy: [desc(blogPostSchema.id)],
		limit: pageSize,
		offset: offset,
	});

	return NextResponse.json({
		status: 200,
		blogPosts,
		pagination: {
			page,
			pageSize,
			totalCount,
			totalPages: Math.ceil(totalCount / pageSize),
		},
	});
}

// Schema for validating blog creation/update requests
const blogRequestSchema = z.object({
	id: z.number().int().optional(),
	slug: z.string().trim().min(1, "Slug is required"),
	title: z.string().trim().min(1, "Title is required"),
	image: z.string().nullable().optional(),
	categoryId: z.number().int().nullable(),
	hasTranslations: z.boolean().optional(),
	languages: z.array(z.string()).optional(),
});

export async function POST(req: Request) {
	if (!checkAuthAdmin()) {
		return NextResponse.json({ status: 401, message: "Not authorized." });
	}

	try {
		const body = await req.json();
		if (process.env.NODE_ENV === "development") {
			console.log("body:", body);
		}
		const validatedData = blogRequestSchema.parse(body);

		const db = getDB();

		if (validatedData.id) {
			// Update existing blog
			const updatedBlog = await db
				.update(blogPostSchema)
				.set({
					slug: validatedData.slug,
					title: validatedData.title,
					image: validatedData.image,
					categoryId: validatedData.categoryId,
				})
				.where(eq(blogPostSchema.id, validatedData.id))
				.returning({ id: blogPostSchema.id });

			if (updatedBlog.length === 0) {
				return NextResponse.json({ status: 404, message: "Blog not found." });
			}

			if (validatedData.languages) {
				validatedData.languages.forEach(async (lang) => {
					await deleteValue(getKVKeyBlogHeads(lang, 1));
				});
			}

			return NextResponse.json({
				status: 200,
				message: "Blog updated successfully",
				blogId: updatedBlog[0].id,
			});
		} else {
			// Create new blog
			const newBlog = await db
				.insert(blogPostSchema)
				.values({
					slug: validatedData.slug,
					title: validatedData.title,
					image: validatedData.image,
					categoryId: validatedData.categoryId,
				})
				.returning({ id: blogPostSchema.id });

			return NextResponse.json({
				status: 200,
				message: "Blog created successfully",
				blogId: newBlog[0].id,
			});
		}
	} catch (error) {
		console.error("Error processing blog request:", error);

		if (error instanceof z.ZodError) {
			return NextResponse.json({
				status: 400,
				message: "Invalid request data",
				errors: error.errors,
			});
		}

		return NextResponse.json({
			status: 500,
			message: "Internal server error",
		});
	}
}
