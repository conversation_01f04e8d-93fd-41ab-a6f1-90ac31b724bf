import { NextResponse } from "next/server";
import { getCurrentSessionUser } from "@/server/auth/auth-session";
import { notifyDevEvent } from "@/server/dev-notify.server";
import { WEBHOST, WEBNAME } from "@/lib/constants";
// import { MembershipID } from "@/@types/membership-type";
// import { getUser } from "@/server/utils-user.server";
import { Polar } from "@polar-sh/sdk";
import { Checkout } from "@polar-sh/sdk/models/components/checkout";
import { mixpanelTrackEvent } from "@/server/mixpanel.server";
import { EVENT_CHECKOUT } from "@/lib/track-events";

interface Params {
	productId?: string;
	type: "subscription" | "onetime";
}
export async function POST(req: Request) {
	const params: Params = await req.json();
	if (!params.productId) {
		return NextResponse.json({ status: 400, message: "No Product ID was provided." });
	}
	const cfIpCountryCode = req.headers.get("cf-ipcountry");
	const cfIp = req.headers.get("cf-connecting-ip");

	// 获取用户信息
	const sessionUser = await getCurrentSessionUser();
	const user = sessionUser;
	// const user = sessionUser ? await getUser(sessionUser.id) : null;
	if (!user) {
		return NextResponse.json({ status: 401, message: "Not authorized." });
	}
	// if (params.type === "subscription") {
	// 	if (user.membershipId !== MembershipID.Free) {
	// 		return NextResponse.json({ status: 1001, message: "You are already a member." });
	// 	}
	// }

	// track mixpanel event
	mixpanelTrackEvent(EVENT_CHECKOUT, user.id, {
		ip: cfIp,
		mp_country_code: cfIpCountryCode,
		productId: params.productId,
	});

	try {
		// Polar.sh
		const polar = new Polar({
			accessToken: process.env.POLAR_ACCESS_TOKEN ?? "",
			server: process.env.NODE_ENV === "production" ? "production" : "sandbox",
		});
		const checkout: Checkout = await polar.checkouts.create({
			allowDiscountCodes: true,
			products: [params.productId],
			customerExternalId: user.id,
			customerName: user.name,
			customerEmail: user.email,
			successUrl: `${WEBHOST}/user/confirmation?checkout_id={CHECKOUT_ID}`,
		});
		const checkoutUrl = checkout.url;

		return NextResponse.json({ status: 200, url: checkoutUrl });
	} catch (error: any) {
		console.log(error);
		notifyDevEvent(`${WEBNAME} - api: /api/payment/checkout`, "Error", error.message, null);
		return NextResponse.json({ status: 500, message: error.message });
	}
}

// export const GET = Checkout({
// 	accessToken: process.env.POLAR_ACCESS_TOKEN!,
// 	successUrl: "/confirmation",
// 	server: process.env.NODE_ENV === "production" ? "production" : "sandbox", // Use this option if you're using the sandbox environment - else use 'production' or omit the parameter
// });
