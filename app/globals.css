@import "tailwindcss";
@import "tw-animate-css";
@plugin "@tailwindcss/typography";

@utility container {
	margin-inline: auto;
	padding-inline: 2rem;
	max-width: 1280px;
}

@custom-variant dark (&:is(.dark *));

@theme inline {
	--color-background: var(--background);
	--color-foreground: var(--foreground);
	--font-sans: var(--font-geist-sans);
	--font-mono: var(--font-geist-mono);
	--font-heading: var(--font-heading);
	--color-sidebar-ring: var(--sidebar-ring);
	--color-sidebar-border: var(--sidebar-border);
	--color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
	--color-sidebar-accent: var(--sidebar-accent);
	--color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
	--color-sidebar-primary: var(--sidebar-primary);
	--color-sidebar-foreground: var(--sidebar-foreground);
	--color-sidebar: var(--sidebar);
	--color-chart-5: var(--chart-5);
	--color-chart-4: var(--chart-4);
	--color-chart-3: var(--chart-3);
	--color-chart-2: var(--chart-2);
	--color-chart-1: var(--chart-1);
	--color-ring: var(--ring);
	--color-input: var(--input);
	--color-border: var(--border);
	--color-destructive: var(--destructive);
	--color-accent-foreground: var(--accent-foreground);
	--color-accent: var(--accent);
	--color-muted-foreground: var(--muted-foreground);
	--color-muted: var(--muted);
	--color-secondary-foreground: var(--secondary-foreground);
	--color-secondary: var(--secondary);
	--color-primary-foreground: var(--primary-foreground);
	--color-primary: var(--primary);
	--color-popover-foreground: var(--popover-foreground);
	--color-popover: var(--popover);
	--color-card-foreground: var(--card-foreground);
	--color-card: var(--card);
	--radius-sm: calc(var(--radius) - 4px);
	--radius-md: calc(var(--radius) - 2px);
	--radius-lg: var(--radius);
	--radius-xl: calc(var(--radius) + 4px);

	--color-action: var(--action);
	--color-action-foreground: var(--action-foreground);
}

:root {
	--radius: 0.75rem;
	--background: oklch(1 0 0);
	--foreground: oklch(0.145 0 0);
	--card: oklch(1 0 0);
	--card-foreground: oklch(0.145 0 0);
	--popover: oklch(1 0 0);
	--popover-foreground: oklch(0.145 0 0);
	--primary: oklch(0.205 0 0);
	--primary-foreground: oklch(0.985 0 0);
	--secondary: oklch(0.97 0 0);
	--secondary-foreground: oklch(0.205 0 0);
	--muted: oklch(0.97 0 0);
	--muted-foreground: oklch(0.556 0 0);
	--accent: oklch(0.97 0 0);
	--accent-foreground: oklch(0.205 0 0);
	--destructive: oklch(0.577 0.245 27.325);
	--border: oklch(0.922 0 0);
	--input: oklch(0.922 0 0);
	--ring: oklch(0.708 0 0);
	--chart-1: oklch(0.646 0.222 41.116);
	--chart-2: oklch(0.6 0.118 184.704);
	--chart-3: oklch(0.398 0.07 227.392);
	--chart-4: oklch(0.828 0.189 84.429);
	--chart-5: oklch(0.769 0.188 70.08);
	--radius: 0.625rem;
	--sidebar: oklch(0.985 0 0);
	--sidebar-foreground: oklch(0.145 0 0);
	--sidebar-primary: oklch(0.205 0 0);
	--sidebar-primary-foreground: oklch(0.985 0 0);
	--sidebar-accent: oklch(0.97 0 0);
	--sidebar-accent-foreground: oklch(0.205 0 0);
	--sidebar-border: oklch(0.922 0 0);
	--sidebar-ring: oklch(0.708 0 0);

	--action: #000000;
	--action-foreground: #ffffff;
}

.dark {
	--background: oklch(0.145 0 0);
	--foreground: oklch(0.985 0 0);
	--card: oklch(0.205 0 0);
	--card-foreground: oklch(0.985 0 0);
	--popover: oklch(0.205 0 0);
	--popover-foreground: oklch(0.985 0 0);
	--primary: oklch(0.922 0 0);
	--primary-foreground: oklch(0.205 0 0);
	--secondary: oklch(0.269 0 0);
	--secondary-foreground: oklch(0.985 0 0);
	--muted: oklch(0.269 0 0);
	--muted-foreground: oklch(0.708 0 0);
	--accent: oklch(0.269 0 0);
	--accent-foreground: oklch(0.985 0 0);
	--destructive: oklch(0.704 0.191 22.216);
	--border: oklch(1 0 0 / 10%);
	--input: oklch(1 0 0 / 15%);
	--ring: oklch(0.556 0 0);
	--chart-1: oklch(0.488 0.243 264.376);
	--chart-2: oklch(0.696 0.17 162.48);
	--chart-3: oklch(0.769 0.188 70.08);
	--chart-4: oklch(0.627 0.265 303.9);
	--chart-5: oklch(0.645 0.246 16.439);
	--sidebar: oklch(0.205 0 0);
	--sidebar-foreground: oklch(0.985 0 0);
	--sidebar-primary: oklch(0.488 0.243 264.376);
	--sidebar-primary-foreground: oklch(0.985 0 0);
	--sidebar-accent: oklch(0.269 0 0);
	--sidebar-accent-foreground: oklch(0.985 0 0);
	--sidebar-border: oklch(1 0 0 / 10%);
	--sidebar-ring: oklch(0.556 0 0);

	--action: #ffffff;
	--action-foreground: #000000;
}

@layer base {
	* {
		@apply border-border outline-ring/50;
	}
	body {
		@apply bg-background text-foreground;
	}
	button {
		@apply cursor-pointer;
	}
}

/* @keyframes hero-gradient-animation {
	0% {
		--s-start-0: 6.688754566361296%;
		--s-end-0: 44.98438254159411%;
		--c-0: hsla(63.5294117647059, 52%, 85%, 1);
		--x-0: 64%;
		--y-0: 12%;
		--c-1: hsla(285.88235294117646, 52%, 85%, 1);
		--y-1: 50%;
		--x-1: 86%;
		--s-start-1: 6.688754566361296%;
		--s-end-1: 44.98438254159411%;
	}

	100% {
		--s-start-0: 2;
		--s-end-0: 51;
		--c-0: hsla(63.5294117647059, 52%, 85%, 1);
		--x-0: 65%;
		--y-0: 34%;
		--c-1: hsla(285.88235294117646, 52%, 85%, 1);
		--y-1: 48%;
		--x-1: 43%;
		--s-start-1: 2;
		--s-end-1: 51;
	}
}

@property --s-start-0 {
	syntax: "<percentage>";
	inherits: false;
	initial-value: 6.688754566361296%;
}

@property --s-end-0 {
	syntax: "<percentage>";
	inherits: false;
	initial-value: 44.98438254159411%;
}

@property --c-0 {
	syntax: "<color>";
	inherits: false;
	initial-value: hsla(63.5294117647059, 52%, 85%, 1);
}

@property --x-0 {
	syntax: "<percentage>";
	inherits: false;
	initial-value: 64%;
}

@property --y-0 {
	syntax: "<percentage>";
	inherits: false;
	initial-value: 12%;
}

@property --c-1 {
	syntax: "<color>";
	inherits: false;
	initial-value: hsla(285.88235294117646, 52%, 85%, 1);
}

@property --y-1 {
	syntax: "<percentage>";
	inherits: false;
	initial-value: 50%;
}

@property --x-1 {
	syntax: "<percentage>";
	inherits: false;
	initial-value: 86%;
}

@property --s-start-1 {
	syntax: "<percentage>";
	inherits: false;
	initial-value: 6.688754566361296%;
}

@property --s-end-1 {
	syntax: "<percentage>";
	inherits: false;
	initial-value: 44.98438254159411%;
}

.your_css_selector_here {
	--c-0: hsla(63.5294117647059, 52%, 85%, 1);
	--x-0: 64%;
	--y-0: 12%;
	--c-1: hsla(285.88235294117646, 52%, 85%, 1);
	--y-1: 50%;
	--x-1: 86%;
	background-color: hsla(60.882352941178326, 0%, 96%, 1);
	background-image:
		radial-gradient(circle at var(--x-0) var(--y-0), var(--c-0) var(--s-start-0), transparent var(--s-end-0)),
		radial-gradient(circle at var(--x-1) var(--y-1), var(--c-1) var(--s-start-1), transparent var(--s-end-1));
	animation: hero-gradient-animation 15s linear infinite alternate;
	background-blend-mode: darken, normal;
} */

.your_css_selector_here {
	background-color: hsla(65, 60%, 97%, 1);
	background-image:
		radial-gradient(circle at 92% 93%, hsla(190, 56%, 91%, 0.53) 11%, transparent 79%),
		radial-gradient(circle at 24% 86%, hsla(13, 52%, 90%, 1) 16%, transparent 83%),
		radial-gradient(circle at 11% 94%, hsla(43, 55%, 90%, 1) 16%, transparent 51%),
		radial-gradient(circle at 36% 38%, hsla(81, 82%, 98%, 1) 3%, transparent 59%),
		radial-gradient(circle at 44% 9%, hsla(252.79411764705875, 81%, 51%, 0.48) 13%, transparent 83%);
	background-blend-mode: normal, normal, normal, normal, normal;
}
