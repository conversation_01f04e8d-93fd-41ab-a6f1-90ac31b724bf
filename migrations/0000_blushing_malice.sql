CREATE TABLE `better_auth_account` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`uid` text NOT NULL,
	`user_uid` text NOT NULL,
	`provider_id` text NOT NULL,
	`account_id` text NOT NULL,
	`access_token` text,
	`refresh_token` text,
	`access_token_expires_at` integer,
	`refresh_token_expires_at` integer,
	`scope` text,
	`id_token` text,
	`created_at` integer NOT NULL,
	`updated_at` integer NOT NULL
);
--> statement-breakpoint
CREATE UNIQUE INDEX `better_auth_account_uid_unique` ON `better_auth_account` (`uid`);--> statement-breakpoint
CREATE INDEX `idx_better_auth_account_user_uid` ON `better_auth_account` (`user_uid`);--> statement-breakpoint
CREATE UNIQUE INDEX `uidx_better_auth_account_provider` ON `better_auth_account` (`account_id`,`provider_id`);--> statement-breakpoint
CREATE TABLE `blog_category` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`slug` text NOT NULL,
	`name` text,
	`description` text,
	`created_at` integer NOT NULL,
	`updated_at` integer NOT NULL
);
--> statement-breakpoint
CREATE UNIQUE INDEX `blog_category_slug_unique` ON `blog_category` (`slug`);--> statement-breakpoint
CREATE TABLE `blog_post` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`slug` text NOT NULL,
	`category_id` integer,
	`title` text,
	`image` text,
	`created_at` integer NOT NULL,
	`updated_at` integer NOT NULL
);
--> statement-breakpoint
CREATE UNIQUE INDEX `blog_post_slug_unique` ON `blog_post` (`slug`);--> statement-breakpoint
CREATE INDEX `idx_blog_post_category` ON `blog_post` (`category_id`);--> statement-breakpoint
CREATE INDEX `idx_blog_post_slug` ON `blog_post` (`slug`);--> statement-breakpoint
CREATE TABLE `blog_post_translation` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`post_id` integer NOT NULL,
	`lang` text DEFAULT 'en' NOT NULL,
	`status` integer DEFAULT 0 NOT NULL,
	`image` text,
	`title` text NOT NULL,
	`meta_title` text,
	`meta_description` text,
	`intro` text,
	`html` text,
	`publish_date` integer NOT NULL,
	`created_at` integer NOT NULL,
	`updated_at` integer NOT NULL,
	FOREIGN KEY (`post_id`) REFERENCES `blog_post`(`id`) ON UPDATE no action ON DELETE cascade
);
--> statement-breakpoint
CREATE INDEX `idx_blog_post_translation_post_id` ON `blog_post_translation` (`post_id`);--> statement-breakpoint
CREATE INDEX `idx_blog_post_translation_lang` ON `blog_post_translation` (`lang`);--> statement-breakpoint
CREATE UNIQUE INDEX `uidx_blog_post_translation_post_id_lang` ON `blog_post_translation` (`post_id`,`lang`);--> statement-breakpoint
CREATE INDEX `idx_blog_post_translation_published_at` ON `blog_post_translation` (`publish_date`);--> statement-breakpoint
CREATE TABLE `changelog` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`version` text,
	`title` text,
	`image` text,
	`publish_date` integer NOT NULL,
	`created_at` integer NOT NULL,
	`updated_at` integer NOT NULL
);
--> statement-breakpoint
CREATE INDEX `idx_changelog_published_at` ON `changelog` (`publish_date`);--> statement-breakpoint
CREATE TABLE `changelog_translation` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`changelog_id` integer NOT NULL,
	`lang` text DEFAULT 'en' NOT NULL,
	`status` integer DEFAULT 0 NOT NULL,
	`image` text,
	`title` text NOT NULL,
	`html` text NOT NULL,
	`publish_date` integer NOT NULL,
	`created_at` integer NOT NULL,
	`updated_at` integer NOT NULL,
	FOREIGN KEY (`changelog_id`) REFERENCES `changelog`(`id`) ON UPDATE no action ON DELETE cascade
);
--> statement-breakpoint
CREATE INDEX `idx_changelog_translation_changelog_id` ON `changelog_translation` (`changelog_id`);--> statement-breakpoint
CREATE INDEX `idx_changelog_translation_lang` ON `changelog_translation` (`lang`);--> statement-breakpoint
CREATE UNIQUE INDEX `uidx_changelog_translation_changelog_id_lang` ON `changelog_translation` (`changelog_id`,`lang`);--> statement-breakpoint
CREATE INDEX `idx_changelog_translation_status` ON `changelog_translation` (`status`);--> statement-breakpoint
CREATE INDEX `idx_changelog_translation_published_at` ON `changelog_translation` (`publish_date`);--> statement-breakpoint
CREATE TABLE `media_head` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`uid` text NOT NULL,
	`user_uid` text NOT NULL,
	`status` text,
	`type` text DEFAULT 'image' NOT NULL,
	`tool` text,
	`task_id` integer,
	`visibility` integer DEFAULT true NOT NULL,
	`prompt` text,
	`video_duration` integer,
	`aspect_ratio` text,
	`seed` text,
	`credits_source` text,
	`remark` text,
	`error_reason` text,
	`created_at` integer NOT NULL,
	`updated_at` integer NOT NULL
);
--> statement-breakpoint
CREATE UNIQUE INDEX `media_head_uid_unique` ON `media_head` (`uid`);--> statement-breakpoint
CREATE INDEX `idx_media_head_user_uid` ON `media_head` (`user_uid`);--> statement-breakpoint
CREATE INDEX `idx_media_head_status` ON `media_head` (`status`);--> statement-breakpoint
CREATE INDEX `idx_media_head_type` ON `media_head` (`type`);--> statement-breakpoint
CREATE TABLE `media_item` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`uid` text NOT NULL,
	`user_uid` text NOT NULL,
	`media_head_uid` text NOT NULL,
	`type` text DEFAULT 'image' NOT NULL,
	`visibility` integer DEFAULT true NOT NULL,
	`media_path` text,
	`created_at` integer NOT NULL,
	`updated_at` integer NOT NULL
);
--> statement-breakpoint
CREATE UNIQUE INDEX `media_item_uid_unique` ON `media_item` (`uid`);--> statement-breakpoint
CREATE INDEX `idx_media_item_user_uid` ON `media_item` (`user_uid`);--> statement-breakpoint
CREATE INDEX `idx_media_item_media_head_uid` ON `media_item` (`media_head_uid`);--> statement-breakpoint
CREATE INDEX `idx_media_item_visibility` ON `media_item` (`visibility`);--> statement-breakpoint
CREATE INDEX `idx_media_item_type` ON `media_item` (`type`);--> statement-breakpoint
CREATE INDEX `idx_media_item_created_at` ON `media_item` (`created_at`);--> statement-breakpoint
CREATE TABLE `media_task` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`user_uid` text NOT NULL,
	`status` integer DEFAULT 0 NOT NULL,
	`type` text DEFAULT 'image' NOT NULL,
	`tool` text,
	`media_head_uid` text,
	`third_request_id` text,
	`visibility` integer DEFAULT true NOT NULL,
	`prompt` text,
	`video_duration` integer,
	`aspect_ratio` text,
	`seed` text,
	`request_body` text,
	`credits_source` text,
	`ip` text,
	`remark` text,
	`error` text,
	`created_at` integer NOT NULL,
	`updated_at` integer NOT NULL
);
--> statement-breakpoint
CREATE INDEX `idx_media_task_user_uid` ON `media_task` (`user_uid`);--> statement-breakpoint
CREATE INDEX `idx_media_task_third_request_id` ON `media_task` (`third_request_id`);--> statement-breakpoint
CREATE TABLE `order` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`user_uid` text NOT NULL,
	`order_id` text NOT NULL,
	`source` text NOT NULL,
	`status` text NOT NULL,
	`billing_reason` text,
	`subscription_id` text,
	`checkout_id` text,
	`product_id` text,
	`currency` text,
	`subtotal_amount` integer,
	`discount_amount` integer,
	`tax_amount` integer,
	`net_amount` integer,
	`total_amount` integer,
	`refunded_amount` integer,
	`refunded_tax_amount` integer,
	`created_at` integer NOT NULL,
	`updated_at` integer NOT NULL
);
--> statement-breakpoint
CREATE UNIQUE INDEX `order_order_id_unique` ON `order` (`order_id`);--> statement-breakpoint
CREATE INDEX `idx_order_user_uid` ON `order` (`user_uid`);--> statement-breakpoint
CREATE TABLE `better_auth_session` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`uid` text NOT NULL,
	`user_uid` text NOT NULL,
	`token` text NOT NULL,
	`expires_at` integer NOT NULL,
	`ip_address` text,
	`user_agent` text,
	`created_at` integer NOT NULL,
	`updated_at` integer NOT NULL
);
--> statement-breakpoint
CREATE UNIQUE INDEX `better_auth_session_uid_unique` ON `better_auth_session` (`uid`);--> statement-breakpoint
CREATE INDEX `idx_better_auth_session_token` ON `better_auth_session` (`token`);--> statement-breakpoint
CREATE TABLE `subscription` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`user_uid` text NOT NULL,
	`subscription_id` text NOT NULL,
	`source` text NOT NULL,
	`status` text NOT NULL,
	`recurring_interval` text NOT NULL,
	`product_id` text NOT NULL,
	`checkout_id` text,
	`current_period_start_at` integer NOT NULL,
	`current_period_end_at` integer,
	`cancel_at_period_end` integer DEFAULT false NOT NULL,
	`canceled_at` integer,
	`start_at` integer,
	`end_at` integer,
	`ended_at` integer,
	`customer_cancellation_reason` text,
	`created_at` integer NOT NULL,
	`updated_at` integer NOT NULL
);
--> statement-breakpoint
CREATE UNIQUE INDEX `subscription_subscription_id_unique` ON `subscription` (`subscription_id`);--> statement-breakpoint
CREATE INDEX `idx_subscription_user_uid` ON `subscription` (`user_uid`);--> statement-breakpoint
CREATE TABLE `user_credits_history` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`user_uid` text NOT NULL,
	`type` text,
	`credits_free` integer,
	`credits_one_time` integer,
	`credits_subscription` integer,
	`remark` text,
	`created_at` integer NOT NULL,
	`updated_at` integer NOT NULL
);
--> statement-breakpoint
CREATE INDEX `idx_user_credits_history_user_uid` ON `user_credits_history` (`user_uid`);--> statement-breakpoint
CREATE TABLE `user` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`uid` text NOT NULL,
	`name` text NOT NULL,
	`email` text NOT NULL,
	`email_verified` integer DEFAULT false,
	`image` text,
	`membership_id` integer DEFAULT 0 NOT NULL,
	`membership_formatted` text DEFAULT 'Free' NOT NULL,
	`credit_free` integer DEFAULT 0 NOT NULL,
	`credit_free_ends_at` integer,
	`credit_onetime` integer DEFAULT 0 NOT NULL,
	`credit_onetime_ends_at` integer,
	`credit_sub` integer DEFAULT 0 NOT NULL,
	`credit_sub_ends_at` integer,
	`sub_id` text,
	`sub_period` text DEFAULT 'none',
	`sub_invoice_ends_at` integer,
	`sub_expire_at` integer,
	`is_deleted` integer DEFAULT false NOT NULL,
	`ban` integer DEFAULT false NOT NULL,
	`country_code` text,
	`ip` text,
	`remark` text,
	`created_at` integer NOT NULL,
	`updated_at` integer NOT NULL
);
--> statement-breakpoint
CREATE UNIQUE INDEX `user_uid_unique` ON `user` (`uid`);--> statement-breakpoint
CREATE UNIQUE INDEX `user_email_unique` ON `user` (`email`);--> statement-breakpoint
CREATE TABLE `better_auth_verification` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`uid` text NOT NULL,
	`identifier` text NOT NULL,
	`value` text NOT NULL,
	`expires_at` integer NOT NULL,
	`created_at` integer NOT NULL,
	`updated_at` integer NOT NULL
);
--> statement-breakpoint
CREATE UNIQUE INDEX `better_auth_verification_uid_unique` ON `better_auth_verification` (`uid`);--> statement-breakpoint
CREATE INDEX `idx_better_auth_verification_identifier` ON `better_auth_verification` (`identifier`);--> statement-breakpoint
CREATE INDEX `idx_better_auth_verification_expires_at` ON `better_auth_verification` (`expires_at`);