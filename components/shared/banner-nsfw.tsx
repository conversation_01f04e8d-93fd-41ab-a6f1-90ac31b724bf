"use client";

import { useEffect, useState } from "react";
import { LightbulbIcon, XIcon } from "lucide-react";
import { Button } from "../ui/button";
import { useSession } from "@/lib/auth-client";

export function WarningBanner({ localStorageKey }: { localStorageKey: string }) {
	const { data: session } = useSession();

	const [isNsfwWarningVisible, setIsNsfwWarningVisible] = useState<boolean>(false);
	const handleCloseNsfwWarning = () => {
		localStorage.setItem(localStorageKey, "true");
		setIsNsfwWarningVisible(false);
	};

	useEffect(() => {
		const hasDismissedNsfwWarning = localStorage.getItem(localStorageKey);
		// if user has never dismissed the warning, show it
		if (!hasDismissedNsfwWarning) {
			setIsNsfwWarningVisible(true);
		}
	}, []);

	if (!session) {
		return null;
	}

	if (!isNsfwWarningVisible) {
		return null;
	}

	return (
		<>
			{isNsfwWarningVisible && (
				<div className="bg-muted w-full max-w-md rounded-lg px-3 py-2">
					<div className="flex items-start justify-between gap-3">
						<div className="flex-1">
							<div className="flex items-center gap-1">
								<LightbulbIcon className="size-4 text-blue-500" />
								<p className="text-[13px]">NSFW content is strictly forbidden.</p>{" "}
							</div>
							<p className="text-muted-foreground text-xs">Any attempt of generating such a content will result in permanent ban.</p>
						</div>
						<Button variant="ghost" size="icon" className="h-8 w-8" onClick={() => handleCloseNsfwWarning()}>
							<XIcon className="size-3.5" />
						</Button>
					</div>
				</div>
			)}
		</>
	);
}
