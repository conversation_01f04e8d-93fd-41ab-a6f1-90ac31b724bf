import { WEBDOMAIN } from "@/lib/constants";
import { cn } from "@/lib/utils";

export const Logo = ({ className }: { className?: string }) => {
	// return <img className={cn("size-8 rounded", className)} src="/static/logo.png" alt={WEBDOMAIN} />;
	return (
		<svg className={cn("size-7 w-7", className)} viewBox="0 0 340 340" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path
				d="M187.995 247.995C187.995 214.858 214.858 187.995 247.995 187.995H299.783C321.874 187.995 339.783 205.903 339.783 227.995V299.783C339.783 321.875 321.874 339.783 299.783 339.783H227.995C205.903 339.783 187.995 321.874 187.995 299.783V247.995Z"
				fill="url(#paint0_linear_6015_125)"
			/>
			<path
				d="M0 40C0 17.9086 17.9086 0 40 0H111.788C133.88 0 151.788 17.9086 151.788 40V91.7884C151.788 124.925 124.925 151.788 91.7883 151.788H40C17.9086 151.788 0 133.88 0 111.788V40Z"
				fill="url(#paint1_linear_6015_125)"
			/>
			<path
				d="M255.319 0.216907H311.932C327.314 0.216907 339.783 12.6863 339.783 28.068V84.6813L339.784 84.6833L339.783 84.684V124.154C339.783 139.536 327.314 152.005 311.932 152.005C311.932 152.005 288 152.005 272.463 152.005C256.925 152.005 209.033 156.272 182.773 182.293C156.512 208.315 151.788 257.357 151.788 272.679C151.788 288 151.788 312.149 151.788 312.149C151.788 327.531 139.319 340 123.937 340H27.8511C12.4694 340 0 327.531 0 312.149V216.063C0 200.681 12.4694 188.212 27.8511 188.212C27.8511 188.212 51.5 188.212 66.8902 188.212C82.2803 188.212 131.573 183.601 157.707 157.227C183.84 130.854 187.99 82.2142 187.995 67.1071C188 52 187.995 28.068 187.995 28.068C187.995 12.6863 200.464 0.216907 215.846 0.216907H254.885L255.102 0L255.319 0.216907Z"
				fill="url(#paint2_linear_6015_125)"
			/>
			<defs>
				<linearGradient id="paint0_linear_6015_125" x1="339.783" y1="339.783" x2="187.995" y2="187.995" gradientUnits="userSpaceOnUse">
					<stop stopColor="#FB3D26" />
					<stop offset="1" stopColor="#CF315B" />
				</linearGradient>
				<linearGradient id="paint1_linear_6015_125" x1="151.788" y1="151.788" x2="5.57022" y2="4.87395" gradientUnits="userSpaceOnUse">
					<stop stopColor="#225BE5" />
					<stop offset="1" stopColor="#165CF3" />
				</linearGradient>
				<linearGradient id="paint2_linear_6015_125" x1="339.783" y1="2.06763e-05" x2="3.73004e-06" y2="339.783" gradientUnits="userSpaceOnUse">
					<stop stopColor="#FC811C" />
					<stop offset="0.292283" stopColor="#FC5828" />
					<stop offset="0.406415" stopColor="#EE4A3F" />
					<stop offset="0.547258" stopColor="#9C3893" />
					<stop offset="0.689316" stopColor="#5442C8" />
					<stop offset="1" stopColor="#2258DF" />
				</linearGradient>
			</defs>
		</svg>
	);
};

export const LogoStroke = ({ className }: { className?: string }) => {
	return (
		<svg className={cn("size-7 w-7", className)} viewBox="0 0 71 25" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path
				d="M61.25 1.625L70.75 1.5625C70.75 4.77083 70.25 7.79167 69.25 10.625C68.2917 13.4583 66.8958 15.9583 65.0625 18.125C63.2708 20.25 61.125 21.9375 58.625 23.1875C56.1667 24.3958 53.4583 25 50.5 25C46.875 25 43.6667 24.2708 40.875 22.8125C38.125 21.3542 35.125 19.2083 31.875 16.375C29.75 14.4167 27.7917 12.8958 26 11.8125C24.2083 10.7292 22.2708 10.1875 20.1875 10.1875C18.0625 10.1875 16.25 10.7083 14.75 11.75C13.25 12.75 12.0833 14.1875 11.25 16.0625C10.4583 17.9375 10.0625 20.1875 10.0625 22.8125L0 22.9375C0 19.6875 0.479167 16.6667 1.4375 13.875C2.4375 11.0833 3.83333 8.64583 5.625 6.5625C7.41667 4.47917 9.54167 2.875 12 1.75C14.5 0.583333 17.2292 0 20.1875 0C23.8542 0 27.1042 0.770833 29.9375 2.3125C32.8125 3.85417 35.7708 5.97917 38.8125 8.6875C41.1042 10.7708 43.1042 12.3333 44.8125 13.375C46.5625 14.375 48.4583 14.875 50.5 14.875C52.6667 14.875 54.5417 14.3125 56.125 13.1875C57.75 12.0625 59 10.5 59.875 8.5C60.7917 6.5 61.25 4.20833 61.25 1.625Z"
				fill="none"
				strokeWidth={0.5}
				stroke="currentColor"
			/>
		</svg>
	);
};
