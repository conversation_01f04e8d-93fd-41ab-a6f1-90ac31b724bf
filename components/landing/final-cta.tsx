import { buttonVariants } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { ROUTE_PATH_SIGN_IN } from "@/lib/constants";
import { NoPrefetchLink } from "../ui/custom/no-prefetch-link";
import { ComponentProps } from "react";

export default function FinalCTA({
	ctaText = "Start For Free",
	ctaTextDisplay,
	ctaUrl = ROUTE_PATH_SIGN_IN,
	ctaClassName,
	title = "",
	description,
	className,
	...props
}: {
	ctaText?: string;
	ctaTextDisplay?: boolean;
	ctaUrl?: string;
	ctaClassName?: string;
	title?: string;
	description?: string;
} & ComponentProps<"div">) {
	return (
		<div className={cn("px-4 py-24 md:px-6", className)} {...props}>
			<div className="container px-0">
				<div className="flex flex-col items-center justify-center gap-12">
					<div className="text-center">
						<h2 className="text-3xl font-semibold text-pretty">{title}</h2>
						{description && <p className="text-secondary-foreground mx-auto mt-4 max-w-3xl text-pretty">{description}</p>}
					</div>

					<div className="flex flex-col items-center gap-3">
						{ctaTextDisplay ? (
							<NoPrefetchLink
								href={ctaUrl}
								className={cn(buttonVariants({ variant: "default" }), "rounded-full bg-blue-500 text-base hover:bg-blue-500/80", ctaClassName)}
							>
								{ctaText}
							</NoPrefetchLink>
						) : (
							<NoPrefetchLink
								href={ctaUrl}
								className={cn(
									buttonVariants({ size: "lg", variant: "default" }),
									"h-12 rounded-full px-8 after:content-(--content)",
									ctaClassName,
								)}
								style={{ "--content": `'${ctaText}'` } as React.CSSProperties}
							></NoPrefetchLink>
						)}
					</div>
				</div>
			</div>
		</div>
	);
}
