import { type LucideIcon } from "lucide-react";
import { cn } from "@/lib/utils";
import { ComponentProps } from "react";

export function GridSections({
	title,
	description,
	features,
	className,
	...props
}: {
	title?: string;
	description?: string;
	features: {
		title: string;
		description: string;
		icon: LucideIcon;
	}[];
} & ComponentProps<"div">) {
	return (
		<div className={cn("bg-[#f9f9f6] py-20", className)}>
			<div className="container flex flex-col items-center gap-12 px-6 py-8" {...props}>
				{title && (
					<div className="text-center">
						<h2 className="text-4xl font-semibold text-pretty">{title}</h2>
						{description && <p className="text-muted-foreground mx-auto mt-4 max-w-4xl text-pretty">{description}</p>}
					</div>
				)}

				<div className="mt-8 grid w-full grid-cols-1 gap-16 sm:grid-cols-2 lg:grid-cols-3">
					{features.map((feature, index) => (
						<div key={index} className="group text-secondary-foreground flex flex-col items-start gap-4 rounded-xl transition-all duration-300">
							<div className="mb-2 flex items-center justify-center rounded-lg">
								<feature.icon className="text-action size-8 transition-transform duration-300" strokeWidth={1.5} />
							</div>
							<h3 className="text-center text-lg font-medium">{feature.title}</h3>
							<p className="text-muted-foreground -mt-2">{feature.description}</p>
						</div>
					))}
				</div>
			</div>
		</div>
	);
}
