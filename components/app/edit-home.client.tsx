"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { useSession } from "@/lib/auth-client";
import { useSignInBoxOpenStore } from "@/store/useSignInBoxOpenStore";
import { ArrowUpIcon } from "lucide-react";
import { useRouter } from "nextjs-toploader/app";

export default function EditHomeClient() {
	const router = useRouter();
	const { data: session } = useSession();
	const { setSignInBoxOpen } = useSignInBoxOpenStore();

	const [prompt, setPrompt] = useState("");

	return (
		<div className="group border-input bg-background mx-auto max-w-xl overflow-hidden rounded-xl border">
			<Textarea
				placeholder="Write what you want to change in your image" // edit image
				value={prompt}
				maxLength={5000}
				onChange={(e) => setPrompt(e.target.value)}
				className="text-secondary-foreground max-h-[56px] min-h-[56px] resize-none border-none text-sm shadow-none placeholder:font-[350] focus-visible:border-none focus-visible:ring-0 [&::-webkit-scrollbar]:hidden"
			/>
			<div className="flex flex-row flex-wrap items-center justify-between gap-1 px-2 py-1.5">
				<Button
					variant="secondary"
					size="sm"
					className="hover:bg-input/60 justify-between rounded-full text-[12px] font-normal shadow-none"
					onClick={() => {
						if (session) {
							router.push("/edit");
						} else {
							setSignInBoxOpen(true);
						}
					}}
				>
					Add image
				</Button>
				<Button
					variant="default"
					size="icon"
					className="size-8"
					onClick={() => {
						if (session) {
							router.push("/edit");
						} else {
							setSignInBoxOpen(true);
						}
					}}
				>
					<ArrowUpIcon className="size-4" />
				</Button>
			</div>
		</div>
	);
}
