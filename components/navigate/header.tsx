"use client";

import React, { useState } from "react";
import { CreditCard, MailIcon, GemIcon, LogOutIcon, XIcon, MenuIcon, CoinsIcon, FolderIcon } from "lucide-react";
import { <PERSON><PERSON>, buttonVariants } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { Logo } from "@/components/logo";
import { useSession } from "@/lib/auth-client";
import { EMAIL_CONTACT, WEBNAME } from "@/lib/constants";
import { Separator } from "@/components/ui/separator";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import {
	NavigationMenu,
	NavigationMenuContent,
	NavigationMenuItem,
	NavigationMenuLink,
	NavigationMenuList,
	NavigationMenuTrigger,
} from "@/components/ui/navigation-menu";
import { useSignInBoxOpenStore } from "@/store/useSignInBoxOpenStore";
import { DropdownMenu, DropdownMenuContent, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useUserStore } from "@/store/useUserStore";
import { MembershipID } from "@/@types/membership-type";
import { usePlanBoxOpenStore } from "@/store/usePlanBoxOpenStore";
import { useSignOut } from "@/hooks/use-signout";
import { Badge } from "../ui/badge";
import { EVENT_OPEN_PLAN_ID } from "@/lib/track-events";
import Link from "next/link";
import { NoPrefetchLink } from "../ui/custom/no-prefetch-link";

interface MenuItem {
	name: string;
	href?: string;
	icon?: React.ReactNode;
	target?: string;
	prefetch?: boolean;
	hidden?: boolean;
	items?: {
		name: string;
		href: string;
		target?: string;
		prefetch?: boolean;
		description?: string;
		new?: boolean;
	}[];
}

const menuItems: MenuItem[] = [
	{ name: "Edit", href: "/edit" },
	// { name: "Tools", href: "/image-editing-tools" },
	{ name: "Pricing", href: "/pricing" },
	// { name: "Blog", href: "/blog" },
];

export const Header = () => {
	const { data: session } = useSession();
	const { setSignInBoxOpen } = useSignInBoxOpenStore();
	const { setPlanBoxOpen } = usePlanBoxOpenStore();
	const { handleSignOut } = useSignOut();
	const { user, creditsAll: userCreditsAll, hasPaid: userHasPaid } = useUserStore();

	const [showMobileMenu, setShowMobileMenu] = useState<boolean>(false);

	const userMenu = () => {
		return (
			<DropdownMenu modal={false}>
				<DropdownMenuTrigger asChild className="cursor-pointer">
					<div className="bg-input flex h-8 w-8 shrink-0 flex-row items-center justify-center gap-2 rounded-full">
						<Avatar className="size-8">
							<AvatarImage src={session?.user.image!} alt="User Avatar" />
							<AvatarFallback>{session?.user.name}</AvatarFallback>
						</Avatar>
						{/* <ChevronDown className="text-muted-foreground size-3.5 shrink-0 lg:hidden" /> */}
					</div>
				</DropdownMenuTrigger>
				<DropdownMenuContent className="w-[240px] rounded-2xl border-none p-0" align="end" forceMount>
					<div className="flex gap-5 p-4">
						<Avatar className="flex size-9 shrink-0 items-center gap-2">
							<AvatarImage src={session?.user.image!} alt="User Avatar" />
							<AvatarFallback>{session?.user.name}</AvatarFallback>
						</Avatar>
						<div className="flex min-w-0 flex-1 flex-col items-start">
							<p className="truncate text-sm font-semibold">{session?.user.name ?? WEBNAME}</p>
							<p className="text-muted-foreground mt-1 truncate text-xs">{session?.user.email ?? "--"}</p>
						</div>
					</div>
					{user?.membershipId === MembershipID.Free && (
						<div className="px-[16px] pb-4">
							<Button size="sm" className="w-full bg-blue-500 hover:bg-blue-500/80" onClick={() => setPlanBoxOpen(true)}>
								Get a plan
							</Button>
						</div>
					)}
					<Separator className="bg-muted" />

					<div className="space-y-1 px-1 py-2">
						<Link href="/assets" className={cn(buttonVariants({ variant: "ghost" }), "w-full justify-between px-3 text-xs font-normal")}>
							<div className="flex flex-row items-center">
								<FolderIcon className="mr-3 h-4 w-4 shrink-0" />
								My Assets
							</div>
						</Link>
						<Link href="/user/my-billing" className={cn(buttonVariants({ variant: "ghost" }), "w-full justify-between px-3 text-xs font-normal")}>
							<div className="flex flex-row items-center">
								<CreditCard className="mr-3 h-4 w-4 shrink-0" />
								My Billing
							</div>
							<p className="flex items-center gap-1 rounded bg-zinc-300 px-2 py-0.5 text-[10px] font-medium">{user?.membershipFormatted}</p>
						</Link>
						<a
							href={`mailto:${EMAIL_CONTACT}`}
							// target="_blank"
							className={cn(buttonVariants({ variant: "ghost" }), "w-full justify-between px-3 text-xs font-normal")}
						>
							<div className="flex flex-row items-center">
								<MailIcon className="mr-3 h-4 w-4 shrink-0" />
								Contact Us
							</div>
						</a>
					</div>

					<Separator className="bg-muted" />

					<div className="space-y-1 px-1 py-2">
						<button className={cn(buttonVariants({ variant: "ghost" }), "w-full justify-between px-3 text-xs font-normal")} onClick={handleSignOut}>
							<div className="flex flex-row items-center">
								<LogOutIcon className="mr-3 h-4 w-4 shrink-0" />
								Sign out
							</div>
						</button>
					</div>
				</DropdownMenuContent>
			</DropdownMenu>
		);
	};

	return (
		<header className="sticky top-0 z-10 w-full bg-white px-4 transition-colors duration-300 md:px-6">
			{/* <header className="fixed top-0 z-10 w-full transition-colors duration-300"> */}
			{/* <BannerTop /> */}
			<div className="relative container mx-auto flex h-16 flex-wrap items-center justify-between px-0">
				<div className="z-20 flex flex-row items-center gap-8">
					{/* <button className="-mr-2 flex cursor-pointer md:hidden" onClick={() => setShowMobileMenu(true)}>
						<Icons.SidebarMynaUI className="text-muted-foreground/80 size-5" />
					</button> */}
					<Link href="/" className="flex items-center gap-2 rtl:space-x-reverse">
						<Logo className="size-7 rounded" />
						<span className="text-xl font-medium">{WEBNAME}</span>
					</Link>
				</div>

				<div className="absolute inset-0 hidden flex-1 justify-center rounded-lg font-normal md:flex md:flex-row">
					<NavigationMenu viewport={false}>
						<NavigationMenuList className="space-x-0">
							{menuItems.map((route, index) => (
								<React.Fragment key={index}>
									{route.items ? (
										<NavigationMenuItem>
											<NavigationMenuTrigger className="px-3 font-normal">
												<p className="text-base">{route.name}</p>
											</NavigationMenuTrigger>
											<NavigationMenuContent>
												<div className="space-y-2">
													{/* <p className="text-sm font-medium">{route.name}</p> */}
													<div className="flex w-[240px] flex-col gap-0.5">
														{route.items.map((feature, index) => (
															<NavigationMenuLink key={index} asChild>
																<Link href={feature.href}>
																	<div className="flex flex-row items-center">
																		{feature.name}
																		{feature.new && (
																			<Badge
																				variant="secondary"
																				className="ml-1 rounded-full bg-green-500 px-1.5 py-0.5 text-[10px]"
																			>
																				New
																			</Badge>
																		)}
																	</div>
																	{feature.description && <div className="text-muted-foreground">{feature.description}</div>}
																</Link>
															</NavigationMenuLink>
														))}
													</div>
												</div>
											</NavigationMenuContent>
										</NavigationMenuItem>
									) : (
										<NavigationMenuItem>
											<NavigationMenuLink className="px-3" asChild>
												<Link href={route.href!} className="flex flex-row items-center" target={route.target} prefetch={route.prefetch}>
													<p className="text-base">{route.name}</p>
													{route.icon && <>{route.icon}</>}
												</Link>
											</NavigationMenuLink>
										</NavigationMenuItem>
									)}
								</React.Fragment>
							))}
							{session && (
								<NavigationMenuItem>
									<NavigationMenuLink className="px-3" asChild>
										<NoPrefetchLink href="/assets" className="flex flex-row items-center">
											<p className="text-base">My Assets</p>
										</NoPrefetchLink>
									</NavigationMenuLink>
								</NavigationMenuItem>
							)}
						</NavigationMenuList>
					</NavigationMenu>
				</div>

				<div className="z-20 flex flex-row items-center gap-2">
					{session ? (
						<>
							{user?.membershipId === MembershipID.Free && (
								<Button
									id={EVENT_OPEN_PLAN_ID}
									size="sm"
									className="h-8 cursor-pointer bg-blue-500 text-[13px] font-normal hover:bg-blue-500/80"
									onClick={() => setPlanBoxOpen(true)}
								>
									<GemIcon className="size-3.5" />
									Upgrade
								</Button>
							)}
							<div className="bg-muted hidden h-8 flex-row items-center gap-1 rounded-lg px-3 py-1.5 text-sm text-green-500 md:flex">
								<CoinsIcon className="size-4" /> <span>{userCreditsAll}</span>
							</div>
							<div className="">{userMenu()}</div>
						</>
					) : (
						<Button onClick={() => setSignInBoxOpen(true)} className="bg-foreground hover:bg-foreground/80 hidden rounded-full md:block">
							Sign In
						</Button>
					)}
					<button className="flex cursor-pointer md:hidden" onClick={() => setShowMobileMenu(true)}>
						<MenuIcon className="text-secondary-foreground/80 size-5" />
					</button>
				</div>
			</div>

			<div
				className={cn(
					"bg-background fixed inset-0 z-50 flex h-screen flex-col duration-200",
					showMobileMenu ? "animate-in fade-in-0 block" : "animate-out fade-out-0 hidden",
				)}
			>
				<div className="flex h-full flex-col">
					<div className="flex h-16 items-center border-b border-gray-100">
						<div className="flex h-full w-full flex-row items-center justify-between px-4 md:px-6">
							<Link href="/" className="flex items-center gap-2 rtl:space-x-reverse" onClick={() => setShowMobileMenu(false)}>
								<Logo className="size-7 rounded" />
								<span className="text-xl font-medium">{WEBNAME}</span>
							</Link>
							<div className="flex flex-row items-center gap-2">
								{session && userMenu()}
								<button onClick={() => setShowMobileMenu(false)}>
									<XIcon className="text-secondary-foreground/80 size-5" />
									<span className="sr-only">Close</span>
								</button>
							</div>
						</div>
					</div>
					<div className="flex-1 overflow-y-scroll py-6">
						{menuItems.map((route, index) => (
							<React.Fragment key={index}>
								{route.items ? (
									<div className="space-y-2">
										<Accordion type="single" collapsible>
											<AccordionItem value="item-1">
												<AccordionTrigger className="cursor-pointer px-6 py-3 text-base font-normal hover:no-underline">
													{route.name}
												</AccordionTrigger>
												<AccordionContent className="">
													{route.items.map((feature, index) => (
														<div key={index} className="">
															<Link
																href={feature.href}
																prefetch={feature.prefetch}
																className="text-secondary-foreground/65 text-sm"
																onClick={() => setShowMobileMenu(false)}
															>
																<div className="hover:bg-muted items-center px-6 py-3">{feature.name}</div>
															</Link>
														</div>
													))}
												</AccordionContent>
											</AccordionItem>
										</Accordion>
									</div>
								) : (
									<Link
										href={route.href!}
										prefetch={route.prefetch ?? true}
										className="text-secondary-foreground"
										target={route.target}
										onClick={() => setShowMobileMenu(false)}
									>
										<div className="hover:bg-muted/60 items-center px-6 py-3">
											{route.name}
											{route.icon && <>{route.icon}</>}
										</div>
									</Link>
								)}
							</React.Fragment>
						))}
						{session && (
							<NoPrefetchLink href="/assets" className="text-secondary-foreground" onClick={() => setShowMobileMenu(false)}>
								<div className="hover:bg-muted/60 items-center px-6 py-3">My Assets</div>
							</NoPrefetchLink>
						)}
					</div>
					<div className="space-y-4 p-6">
						{!session && (
							<Button size="lg" onClick={() => setSignInBoxOpen(true)} className="bg-foreground hover:bg-foreground/80 w-full rounded-full">
								Sign In
							</Button>
						)}
					</div>
				</div>
			</div>

			{/* <Dialog open={showMobileMenu} onOpenChange={setShowMobileMenu}>
				<DialogPortal data-slot="dialog-portal">
					<DialogPrimitive.Content
						data-slot="dialog-content"
						className={cn(
							"bg-background fixed top-[50%] left-[50%] z-50 flex h-[100vh] w-full translate-x-[-50%] translate-y-[-50%] flex-col duration-200",
							"data-[state=open]:animate-in data-[state=open]:fade-in-0",
							"data-[state=closed]:animate-out data-[state=closed]:fade-out-0",
						)}
					>
						<DialogHeader className="h-14 gap-0 border-b">
							<DialogTitle className="flex h-full flex-row items-center justify-between px-4 md:px-6">
								<Link href="/" className="flex items-center gap-1 rtl:space-x-reverse" onClick={() => setShowMobileMenu(false)}>
									<Logo className="size-10 rounded" />
									<span className="font-heading text-xl font-semibold">{WEBNAME}</span>
								</Link>
								<div className="flex flex-row items-center gap-1">
									{session && userMenu()}
									<DialogClose>
										<XIcon className="text-secondary-foreground/80 size-5" />
										<span className="sr-only">Close</span>
									</DialogClose>
								</div>
							</DialogTitle>
							<DialogDescription className="sr-only"></DialogDescription>
						</DialogHeader>
						<div className="flex-1 py-6">
							{menuItems.map((route, index) => (
								<React.Fragment key={index}>
									{route.items ? (
										<div className="space-y-2">
											<Accordion type="single" collapsible>
												<AccordionItem value="item-1">
													<AccordionTrigger className="cursor-pointer px-6 py-3 text-base font-normal hover:no-underline">
														{route.name}
													</AccordionTrigger>
													<AccordionContent className="">
														{route.items.map((feature, index) => (
															<div key={index} className="">
																<Link
																	href={feature.href}
																	prefetch={feature.prefetch}
																	className="text-secondary-foreground/65 text-sm"
																	onClick={() => setShowMobileMenu(false)}
																>
																	<div className="hover:bg-muted items-center px-6 py-3">{feature.name}</div>
																</Link>
															</div>
														))}
													</AccordionContent>
												</AccordionItem>
											</Accordion>
										</div>
									) : (
										<Link
											href={route.href!}
											prefetch={route.prefetch ?? true}
											className="text-secondary-foreground"
											target={route.target}
											onClick={() => setShowMobileMenu(false)}
										>
											<div className="hover:bg-muted/60 items-center px-6 py-3">
												{route.name}
												{route.icon && <>{route.icon}</>}
											</div>
										</Link>
									)}
								</React.Fragment>
							))}
						</div>
						<DialogFooter className="space-y-4 p-6">
							{!session && (
								<Button size="lg" onClick={() => setSignInBoxOpen(true)} className="bg-foreground hover:bg-foreground/80 w-full">
									Sign In
								</Button>
							)}
						</DialogFooter>
					</DialogPrimitive.Content>
				</DialogPortal>
			</Dialog> */}
		</header>
	);
};
