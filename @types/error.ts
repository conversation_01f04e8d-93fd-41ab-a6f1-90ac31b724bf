export class ParamsError extends Error {
	// 400：请求参数不足/不合规
	public readonly statusCode: number = 400;

	constructor(message: string) {
		super(message);
		this.name = "ParamsError";
	}
}
export class AuthError extends Error {
	// 401：未登录/未授权
	public readonly statusCode: number = 401;

	constructor(message: string) {
		super(message);
		this.name = "AuthError";
	}
}
export class Credits402Error extends Error {
	// 402：剩余token数量不足
	public readonly statusCode: number = 402;

	constructor(message: string) {
		super(message);
		this.name = "Credits402Error";
	}
}
export class NotFoundError extends Error {
	// 404：未找到
	public readonly statusCode: number = 404;

	constructor(message: string) {
		super(message);
		this.name = "NotFoundError";
	}
}
// 422
export class UnprocessableError extends Error {
	// 422：请求参数不正确
	public readonly statusCode: number = 422;

	constructor(message: string) {
		super(message);
		this.name = "UnprocessableError";
	}
}
export class ServerError extends Error {
	// 500：服务器在处理请求时遇到了未预料的情况
	public readonly statusCode: number = 500;

	constructor(message: string) {
		super(message);
		this.name = "ServerError";
	}
}
export class ServerFetchError extends Error {
	// 501：服务器在fetch时遇到了未预料的情况
	public readonly statusCode: number = 501;

	constructor(message: string) {
		super(message);
		this.name = "ServerFetchError";
	}
}

export class IgnoreError extends Error {
	constructor(message: string) {
		super(message);
		this.name = "IgnoreError";
	}
}

export const handleError = (status: number | undefined | null, message: string | undefined | null) => {
	if (!status) return;

	if (status === 200) return;

	switch (status) {
		case 400:
			throw new ParamsError(message as string);
		case 401:
			throw new AuthError(message as string);
		case 402:
			throw new Credits402Error(message as string);
		case 404:
			throw new NotFoundError(message as string);
		case 422:
			throw new UnprocessableError(message as string);
		case 500:
			throw new ServerError(message as string);
		case 501:
			throw new ServerFetchError(message as string);
		default:
			throw new Error(message as string);
	}
};
