type Badge = {
	href: string;
	src?: string;
	alt: string;
	title?: string;
};

export const footerBadges: Badge[] = [
	// { 提交完整且Google已收录
	// 	href: "https://code.market?code.market=verified",
	// 	src: "https://code.market/assets/manage-product/featured-logo-dark.svg",
	// 	alt: "Dreampik Spotlight on ai tools code.market",
	// },
	// { 提交完整且Google已收录
	// 	href: "https://dang.ai/",
	// 	src: "https://cdn.prod.website-files.com/63d8afd87da01fb58ea3fbcb/6487e2868c6c8f93b4828827_dang-badge.png",
	// 	alt: "Dreampik Spotlight on Dang.ai",
	// },
	// { 提交完整且Google已收录
	// 	href: "https://twelve.tools",
	// 	src: "https://twelve.tools/badge2-dark.svg",
	// 	alt: "Dreampik featured on Twelve Tools",
	// },
];
