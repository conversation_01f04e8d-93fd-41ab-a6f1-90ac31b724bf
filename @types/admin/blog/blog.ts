import { BlogPost } from "@/server/db/schema.server";
import { z } from "zod";

export enum BlogStatus {
	Draft = 0,
	Published = 1,
}

export const getBlogStatusText = (status: number) => {
	switch (status) {
		case BlogStatus.Draft:
			return "Draft";
		case BlogStatus.Published:
			return "Published";
		default:
			return "Unknown";
	}
};

// Blog category for admin
export const blogCategoryTypeSchema = z.object({
	id: z.number().int().optional(),
	slug: z.string().optional(),
	name: z.string().trim().nonempty({
		message: "Name is required",
	}),
});
export type BlogCategorySchemaType = z.infer<typeof blogCategoryTypeSchema>;

// Blog Head
export const blogPublicHeadSchema = z.object({
	categoryId: z.number().int().nullable(),
	lang: z.string(),
	slug: z.string(),
	title: z.string(),
	image: z.string().nullable(),
	intro: z.string().nullable(),
	publishedAt: z.date(),
});
export type BlogPublicHead = z.infer<typeof blogPublicHeadSchema>;
export const blogPublicHeadArraySchema = z.array(blogPublicHeadSchema);

export type PartialBlogPostTranslation = {
	id: number;
	postId: number;
	lang: string;
	status: number;
	title: string;
	publishedAt: Date;
	createdAt: Date;
	updatedAt: Date;
};
export type BlogPostWithPartialTranslations = BlogPost & {
	translations: PartialBlogPostTranslation[];
};

// Blog detail
export const blogDetailSchema = z.object({
	categoryId: z.number().int().nullable(),
	slug: z.string(),
	lang: z.string(),
	metaTitle: z.string().nullable(),
	metaDescription: z.string().nullable(),
	title: z.string(),
	image: z.string().nullable(),
	intro: z.string().nullable(),
	html: z.string().nullable(),
	publishedAt: z.date(),
	status: z.number().int(),
});
export type BlogDetailType = z.infer<typeof blogDetailSchema>;
