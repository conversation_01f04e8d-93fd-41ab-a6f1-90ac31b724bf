import { Changelog } from "@/server/db/schema-changelog.server";
import { z } from "zod";

export enum ChangelogStatus {
	Draft = 0,
	Published = 1,
}

export const getChangelogStatusText = (status: number) => {
	switch (status) {
		case ChangelogStatus.Draft:
			return "Draft";
		case ChangelogStatus.Published:
			return "Published";
		default:
			return "Unknown";
	}
};

// Changelog Head
export const changelogHeadSchema = z.object({
	id: z.number().int().optional(),
	lang: z.string().trim().nonempty({
		message: "Language is required",
	}),
	version: z.string().nullable(),
	title: z.string().trim().nonempty({
		message: "Title is required",
	}),
	image: z.string().nullable(),
	html: z.string().nullable(),
	publishedAt: z.date(),
	status: z.number().int(),
});
export type ChangelogHead = z.infer<typeof changelogHeadSchema>;

export type PartialChangelogTranslation = {
	id: number;
	changelogId: number;
	lang: string;
	status: number;
	title: string;
	publishedAt: Date;
	createdAt: Date;
	updatedAt: Date;
};
export type ChangelogWithPartialTranslations = Changelog & {
	translations: PartialChangelogTranslation[];
};
