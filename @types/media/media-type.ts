export enum MediaHeadType {
	Image = "image",
	Video = "video",
}

export enum MediaHeadToolType {
	TextToImage = "text-to-image",
	ImageToImage = "image-to-image",
	TextToVideo = "text-to-video",
	ImageToVideo = "image-to-video",
	ImageTool = "image-tool",
	ImageEditor = "image-editor",
	PhotoEffect = "photo-effect",
}

export enum MediaResultStatus {
	PreparingFile = "preparing_file",
	UploadingFile = "uploading_file",
	Queued = "queued",
	InProgress = "in_progress",
	Completed = "completed",
	Failed = "failed",
	Expire = "expire",
}
const mediaResultStatusInfos = [
	{
		status: MediaResultStatus.PreparingFile,
		statusText: "Preparing...",
		formatted: "Preparing",
	},
	{
		status: MediaResultStatus.UploadingFile,
		statusText: "Uploading...",
		formatted: "Uploading",
	},
	{
		status: MediaResultStatus.Queued,
		statusText: "Creating task...",
		formatted: "Queuing",
	},
	{
		status: MediaResultStatus.InProgress,
		statusText: "Generating...",
		formatted: "InProgress",
	},
	{
		status: MediaResultStatus.Completed,
		statusText: "Completed 🎉",
		formatted: "Completed",
	},
	{
		status: MediaResultStatus.Failed,
		statusText: "Generation failed 😢, please try again or contact support",
		formatted: "Failed",
	},
];
export const getMediaResultStatusText = (status: string) => {
	const statusInfo = mediaResultStatusInfos.find((statusInfo) => statusInfo.status === status);
	return statusInfo?.statusText ?? "Unknown";
};
export const getMediaResultStatusFormatted = (status: string) => {
	const statusInfo = mediaResultStatusInfos.find((statusInfo) => statusInfo.status === status);
	return statusInfo?.formatted ?? "Unknown";
};
