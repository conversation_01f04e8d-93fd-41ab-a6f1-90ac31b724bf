import { NextResponse } from "next/server";
import { <PERSON>th<PERSON><PERSON><PERSON>, Para<PERSON><PERSON>rror, Credits402Error, UnprocessableError } from "@/@types/error";
import { notifyDevEvent } from "@/server/dev-notify.server";
import { ValidationError } from "@fal-ai/client";

export function handleApiError(error: any, eventName: string, options?: string) {
	console.error("API error:", error);

	const errorClasses = [AuthError, ParamsError, Credits402Error, UnprocessableError];
	const knownError = errorClasses.find((errorClass) => error instanceof errorClass);

	if (knownError) {
		return NextResponse.json({
			status: error.statusCode,
			message: error.message,
		});
	}

	notifyDevEvent(eventName, "Error", error.message, options);
	return NextResponse.json({ status: 500, message: error.message });
}

export function constructErrorFalAI(error: any): Error {
	if (error instanceof ValidationError) {
		const errorDetail = error.body.detail[0];
		const errorStatus = error.status;
		const errorType = errorDetail.type;
		if (errorStatus === 422 && errorType === "content_policy_violation") {
			return new UnprocessableError(errorDetail.msg);
		}
		if (errorStatus === 422 && errorType === "value_error") {
			return new UnprocessableError("Cannot generate an image with your input. Please try again using different input.");
		}
	}
	return error;
}
