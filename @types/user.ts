export interface SessionUser {
	id: string;
	name: string;
	email: string;
	image: string | null;
}

export interface UserInfoDB {
	id: string;
	name: string;
	email: string;
	image: string | null;
	membershipId: number;
	membershipFormatted: string;

	membershipLevel?: string; // Google Analytics use

	creditFree: number;
	creditFreeEndsAt: Date | null;
	creditOneTime: number;
	creditOneTimeEndsAt: Date | null;
	creditSubscription: number;
	creditSubscriptionEndsAt: Date | null;

	subscriptionId: string | null;
	subscriptionPeriod: string | null;
	subscriptionExpireAt: Date | null;
}
