// ==================== Membership ID ====================
export enum MembershipID {
	Free = 0,
	Starter = 10,
	Pro = 20,
	Premium = 30,
	Ultra = 40,
}

// ==================== Membership ID Type ====================
export interface MembershipType {
	id: number;
	name: string;
	credits: number;
}

export const membershipMapping: Record<MembershipID, MembershipType> = {
	[MembershipID.Free]: {
		id: MembershipID.Free,
		name: "Free",
		credits: 10,
	},
	[MembershipID.Starter]: {
		id: MembershipID.Starter,
		name: "Starter",
		credits: 200,
	},
	[MembershipID.Pro]: {
		id: MembershipID.Pro,
		name: "Pro",
		credits: 1000,
	},
	[MembershipID.Premium]: {
		id: MembershipID.Premium,
		name: "Premium",
		credits: 2800,
	},
	[MembershipID.Ultra]: {
		id: MembershipID.Ultra,
		name: "Ultra",
		credits: 9000,
	},
};

// ==================== Membership Period Type ====================
export interface MembershipPeriodType {
	value: "none" | "month" | "year";
	name: "None" | "Monthly" | "Yearly";
}

export const MembershipPeriodNone: MembershipPeriodType = {
	value: "none",
	name: "None",
};

export const MembershipPeriodMonth: MembershipPeriodType = {
	value: "month",
	name: "Monthly",
};
export const MembershipPeriodYear: MembershipPeriodType = {
	value: "year",
	name: "Yearly",
};

// ==================== user credit history type ====================
export enum CreditHistoryType {
	ExpireOnetime = "expire_onetime",
	ExpireFree = "expire_free",
	ExpireSubscription = "expire_subscription",
	Consume = "consume",
	Add = "add",
	ResetFree = "reset_free",
	ResetSubscription = "reset_subscription",
}
