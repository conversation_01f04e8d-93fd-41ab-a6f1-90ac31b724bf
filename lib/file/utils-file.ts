import { ofetch } from "ofetch";
import { WEBDOMAIN } from "../constants";
import { getNanoId } from "../utils";

/**
 * Get file extension
 */
export const getFileExtension = (fileUrl: string) => {
	const extension = fileUrl.split(".").pop()?.split("?")[0];
	if (!extension) {
		throw new Error("Invalid file URL");
	}
	return extension;
};
/**
 * 根据 Video URL 后缀名获取 Content-Type。
 *
 * @param videoUrl 图片 URL。
 * @returns  对应的 Content-Type 字符串，如果无法识别则返回 null。
 */
export function getContentTypeFromVideoUrl(videoUrl: string): string | null {
	if (!videoUrl) {
		return null; // 或者抛出错误，根据你的需求
	}

	const urlLower = videoUrl.toLowerCase();

	if (urlLower.endsWith(".mp4")) {
		return "video/mp4";
	} else if (urlLower.endsWith(".webm")) {
		return "video/webm";
	} else if (urlLower.endsWith(".ogg")) {
		return "video/ogg";
	} else if (urlLower.endsWith(".mov")) {
		return "video/quicktime";
	} else if (urlLower.endsWith(".avi")) {
		return "video/x-msvideo";
	} else if (urlLower.endsWith(".wmv")) {
		return "video/x-ms-wmv";
	} else if (urlLower.endsWith(".flv")) {
		return "video/x-flv";
	} else if (urlLower.endsWith(".mkv")) {
		return "video/x-matroska";
	} else if (urlLower.endsWith(".m4a")) {
		return "video/mp4";
	} else {
		return null; // 无法识别的后缀名
	}
}

/**
 * 根据音频 URL 后缀名获取 Content-Type。
 *
 * @param audioUrl 音频 URL。
 * @returns  对应的 Content-Type 字符串，如果无法识别则返回 null。
 */
export function getContentTypeFromAudioUrl(audioUrl: string): string | null {
	if (!audioUrl) {
		return null;
	}

	const urlLower = audioUrl.toLowerCase();

	if (urlLower.endsWith(".mp3")) {
		return "audio/mpeg";
	} else if (urlLower.endsWith(".wav")) {
		return "audio/wav";
	} else if (urlLower.endsWith(".ogg")) {
		return "audio/ogg";
	} else if (urlLower.endsWith(".m4a")) {
		return "audio/mp4";
	} else if (urlLower.endsWith(".aac")) {
		return "audio/aac";
	} else if (urlLower.endsWith(".flac")) {
		return "audio/flac";
	} else if (urlLower.endsWith(".wma")) {
		return "audio/x-ms-wma";
	} else {
		return null; // 无法识别的后缀名
	}
}

/**
 * 根据图片 URL 后缀名获取 Content-Type。
 *
 * @param imageUrl 图片 URL。
 * @returns  对应的 Content-Type 字符串，如果无法识别则返回 null。
 */
export function getContentTypeFromImageUrl(imageUrl: string): string | null {
	if (!imageUrl) {
		return null; // 或者抛出错误，根据你的需求
	}

	const urlLower = imageUrl.toLowerCase();

	if (urlLower.endsWith(".jpg") || urlLower.endsWith(".jpeg")) {
		return "image/jpeg";
	} else if (urlLower.endsWith(".png")) {
		return "image/png";
	} else if (urlLower.endsWith(".gif")) {
		return "image/gif";
	} else if (urlLower.endsWith(".bmp")) {
		return "image/bmp";
	} else if (urlLower.endsWith(".webp")) {
		return "image/webp";
	} else if (urlLower.endsWith(".svg")) {
		return "image/svg+xml";
	} else if (urlLower.endsWith(".tiff") || urlLower.endsWith(".tif")) {
		return "image/tiff";
	} else if (urlLower.endsWith(".ico")) {
		return "image/x-icon";
	} else {
		return null; // 无法识别的后缀名
	}
}

export const imageUrlToBase64 = async (
	url: string,
	options?: {
		noCache?: boolean;
		hasWatermark?: boolean;
	},
): Promise<string> => {
	const blob = await ofetch(url, {
		headers: options?.noCache
			? {
					"Cache-Control": "no-cache", // 如果是r2且刚上传的资源则需要该配置
				}
			: undefined,
		responseType: "blob",
	});
	const base64 = await fileToBase64(blob);

	if (!options?.hasWatermark) return base64;

	return addWatermarkToImage(base64);
};

export const fileToBase64 = (file: Blob): Promise<string> => {
	return new Promise((resolve, reject) => {
		const reader = new FileReader();
		reader.onload = () => {
			if (typeof reader.result === "string") {
				resolve(reader.result);
			} else {
				reject(new Error("Failed to convert file to base64"));
			}
		};
		reader.onerror = () => reject(reader.error);
		reader.readAsDataURL(file);
	});
};

/**
 * Download image from url
 */
export const downloadImageFromUrl = async (imageUrl?: string | null, fileName?: string) => {
	if (!imageUrl) return;
	if (!fileName) fileName = `${getNanoId(6)}`;
	try {
		// const response = await fetch(imageUrl);
		// if (!response.ok) {
		// 	throw new Error(`Download image failed: ${response.status} ${response.statusText}`);
		// }
		// const blob = await response.blob();

		const blob = await ofetch(imageUrl, {
			headers: {
				"Cache-Control": "no-cache",
			},
			responseType: "blob",
		});
		const url = URL.createObjectURL(blob);
		const a = document.createElement("a");

		// 从原始URL中提取文件扩展名
		const extension = getFileExtension(imageUrl);

		a.href = url;
		a.download = `${fileName}.${extension}`;
		document.body.appendChild(a);
		a.click();
		document.body.removeChild(a);
		URL.revokeObjectURL(url);
	} catch (error) {
		console.error("Download image failed:", error);
		throw error;
	}
};

/**
 * Download image from base64
 */
export const downloadImageFromBase64 = async (base64Data: string, fileName?: string) => {
	if (!base64Data) return;
	if (!fileName) fileName = `${getNanoId(6)}`;

	try {
		// Extract the mime type and base64 content
		const [header, content] = base64Data.split(",");
		const mimeType = header.match(/data:(.*?);/)?.[1] || "image/png";
		const extension = mimeType.split("/")[1];

		// Convert base64 to blob
		const byteCharacters = atob(content);
		const byteArrays = new Uint8Array(byteCharacters.length);

		for (let i = 0; i < byteCharacters.length; i++) {
			byteArrays[i] = byteCharacters.charCodeAt(i);
		}

		const blob = new Blob([byteArrays], { type: mimeType });
		const url = URL.createObjectURL(blob);

		// Create and trigger download
		const a = document.createElement("a");
		a.href = url;
		a.download = `${fileName}.${extension}`;
		document.body.appendChild(a);
		a.click();
		document.body.removeChild(a);
		URL.revokeObjectURL(url);
	} catch (error) {
		console.error("Download image failed:", error);
		throw error;
	}
};
export const downloadImageFromBase64WithWatermark = async (base64Data: string, fileName?: string) => {
	const base64 = await addWatermarkToImage(base64Data);
	await downloadImageFromBase64(base64);
};

/**
 * Download file from url
 */
export const downloadFileFromUrl = async (fileUrl?: string | null, fileName?: string) => {
	if (!fileUrl) return;
	if (!fileName) fileName = `${getNanoId(6)}`;
	try {
		const blob = await ofetch(fileUrl, {
			headers: {
				"Cache-Control": "no-cache",
			},
			responseType: "blob",
		});
		const url = URL.createObjectURL(blob);
		const a = document.createElement("a");

		// 从原始URL中提取文件扩展名
		const extension = getFileExtension(fileUrl);

		a.href = url;
		a.download = `${fileName}.${extension}`;
		document.body.appendChild(a);
		a.click();
		document.body.removeChild(a);
		URL.revokeObjectURL(url);
	} catch (error) {
		console.error("Download file failed:", error);
		throw error;
	}
};

/**
 * 为图片的base64数据并添加水印
 * @param base64Data 图片的base64数据
 * @param watermarkText 水印文字，默认为 "YouStylize.com"
 * @returns Promise<string> 返回带水印的base64字符串
 */
export async function addWatermarkToImage(base64Data: string, watermarkText: string = WEBDOMAIN): Promise<string> {
	return new Promise((resolve, reject) => {
		const img = new Image();
		img.src = base64Data;
		img.crossOrigin = "anonymous";

		img.onload = () => {
			try {
				// create canvas
				const canvas = document.createElement("canvas");
				const ctx = canvas.getContext("2d");

				if (ctx) {
					// Enable anti-aliasing
					ctx.imageSmoothingEnabled = true;
					ctx.imageSmoothingQuality = "high";
				} else {
					reject(new Error("Failed to get canvas context"));
					return;
				}

				// Set canvas size to match image
				canvas.width = img.width;
				canvas.height = img.height;

				// draw original image
				ctx.drawImage(img, 0, 0);

				// Set watermark style
				const fontSize = Math.min(img.width, img.height) * 0.08; // Adjust font size based on image size
				ctx.font = `bold ${fontSize}px Arial`;
				ctx.fillStyle = "rgba(255, 255, 255, 0.8)"; // White semi-transparent
				ctx.strokeStyle = "rgba(0, 0, 0, 0.5)"; // Black stroke
				ctx.lineWidth = 2;
				ctx.textAlign = "center";
				ctx.textBaseline = "middle";

				// Calculate watermark position (center of image)
				const centerX = img.width / 2;
				const centerY = img.height / 2;

				// draw watermark stroke
				ctx.strokeText(watermarkText, centerX, centerY);
				// draw watermark text
				ctx.fillText(watermarkText, centerX, centerY);

				// convert canvas to base64
				const base64String = canvas.toDataURL("image/png");
				resolve(base64String);
			} catch (error) {
				reject(error);
			}
		};

		img.onerror = () => {
			reject(new Error("Failed to load image"));
		};
	});
}
