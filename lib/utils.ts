import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";
import * as uuid from "uuid";
import { nanoid } from "nanoid";
import { isNil } from "lodash";
import { i18nConfig } from "@/i18n-config";

export function cn(...inputs: ClassValue[]) {
	return twMerge(clsx(inputs));
}

// >>>>>>>>>>>>>>>>>>>>>Generate ID<<<<<<<<<<<<<<<<<<<<<<<<<<<<
export function getUUIDString(): string {
	return uuid.v7().replace(/-/g, "");
}
export function getNanoId(length?: number): string {
	if (length) return nanoid(length);
	return nanoid();
}

// KV key
export const KV_KEY_USER = "user";
export const getKVKeyUser = (userUid: string) => {
	return `${KV_KEY_USER}:${userUid}`;
};
export const KV_KEY_LATEST_LOGOS = "latest-logos";
export const getKVKeyLatestLogosWithStyleId = (styleId?: number | null) => {
	if (isNil(styleId)) return KV_KEY_LATEST_LOGOS;
	return `${KV_KEY_LATEST_LOGOS}:${styleId}`;
};
// KV key for blog
export const getKVKeyBlogHeads = (lang: string | undefined | null, page: number) => {
	if (lang && lang !== i18nConfig.defaultLocale) {
		return `blogs:${lang}:${page}`;
	}
	return `blogs:${page}`;
};
export const getKVKeyBlog = (lang: string | undefined | null, slug: string) => {
	if (lang && lang !== i18nConfig.defaultLocale) {
		return `blog:${lang}:${slug}`;
	}
	return `blog:${slug}`;
};
export const KV_KEY_BLOG_CATEGORIES = "blog_categories";
// KV key for changelog
export const getKVKeyChangelogHeads = (lang: string | undefined | null, page: number) => {
	if (lang && lang !== i18nConfig.defaultLocale) {
		return `changelogs:${lang}:${page}`;
	}
	return `changelogs:${page}`;
};
// KV key for mediaTask
export const getKVKeyMediaTask = (requestId: string) => {
	return `media_task:${requestId}`;
};

// Download file
export const handleDownloadFileByUrl = async (url?: string) => {
	if (!url) return;
	const response = await fetch(url);
	const blob = await response.blob();
	const link = document.createElement("a");
	link.href = window.URL.createObjectURL(blob);
	link.download = getNanoId(10) + ".wav";
	link.click();
};

export const sleep = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

export function calculateProgress(seconds: number, approximateTime: number): number {
	// 步骤 1: 限制 x 的有效范围在 [0, approximateTime]
	const clampedX = Math.max(0, Math.min(seconds, approximateTime));

	const rawY = (clampedX * 100) / approximateTime;
	let flooredY = Math.floor(rawY);

	const finalY = Math.min(flooredY, 99);

	return finalY;
}
